using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Nexara.Core.Data;
using Nexara.Core.Models;
using System.Text.Json;

namespace Nexara.Api.Controllers;

[ApiController]
[Route("api/sessions/{sessionId}/[controller]")]
public class AnswersController : ControllerBase
{
    private readonly NexaraDbContext _context;
    private readonly ILogger<AnswersController> _logger;

    public AnswersController(NexaraDbContext context, ILogger<AnswersController> logger)
    {
        _context = context;
        _logger = logger;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<Answer>>> GetAnswers(Guid sessionId)
    {
        var answers = await _context.Answers
            .Where(a => a.SessionId == sessionId)
            .Include(a => a.Node)
            .OrderBy(a => a.CreatedAt)
            .ToListAsync();

        return Ok(answers);
    }

    [HttpPost]
    public async Task<ActionResult<Answer>> CreateAnswer(Guid sessionId, CreateAnswerRequest request)
    {
        // Verify session exists and is active
        var session = await _context.Sessions
            .FirstOrDefaultAsync(s => s.Id == sessionId && s.Status == SessionStatus.Active);

        if (session == null)
        {
            return BadRequest("Session not found or not active");
        }

        // Verify node exists
        var node = await _context.SurveyNodes
            .FirstOrDefaultAsync(n => n.Id == request.NodeId && n.SurveyId == session.SurveyId);

        if (node == null)
        {
            return BadRequest("Node not found");
        }

        var answer = new Answer
        {
            SessionId = sessionId,
            NodeId = request.NodeId,
            ValueJson = JsonSerializer.Serialize(request.Value),
            Confidence = request.Confidence
        };

        _context.Answers.Add(answer);

        // Update session activity
        session.LastActivityAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        _logger.LogInformation("Created answer for session {SessionId}, node {NodeId}", sessionId, request.NodeId);

        return CreatedAtAction(nameof(GetAnswer), new { sessionId, id = answer.Id }, answer);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<Answer>> GetAnswer(Guid sessionId, Guid id)
    {
        var answer = await _context.Answers
            .Include(a => a.Node)
            .FirstOrDefaultAsync(a => a.Id == id && a.SessionId == sessionId);

        if (answer == null)
        {
            return NotFound();
        }

        return Ok(answer);
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateAnswer(Guid sessionId, Guid id, UpdateAnswerRequest request)
    {
        var answer = await _context.Answers
            .FirstOrDefaultAsync(a => a.Id == id && a.SessionId == sessionId);

        if (answer == null)
        {
            return NotFound();
        }

        if (request.Value != null)
        {
            answer.ValueJson = JsonSerializer.Serialize(request.Value);
        }

        if (request.Confidence.HasValue)
        {
            answer.Confidence = request.Confidence.Value;
        }

        await _context.SaveChangesAsync();

        _logger.LogInformation("Updated answer {AnswerId} for session {SessionId}", id, sessionId);

        return NoContent();
    }
}

public record CreateAnswerRequest(Guid NodeId, object Value, double? Confidence = null);
public record UpdateAnswerRequest(object? Value = null, double? Confidence = null);