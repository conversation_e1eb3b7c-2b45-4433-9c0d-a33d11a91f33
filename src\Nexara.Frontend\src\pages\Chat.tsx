import { useEffect, useState } from 'react'
import Chat from '../components/Chat'
import { useChatStore } from '../stores/chatStore'
import { ChatConversation, MockConversationScenario } from '../types/chat'

// Mock data for testing
const mockBots = [
  {
    id: 'bot-1',
    name: 'Customer Support Bot',
    description: 'Helps with general customer inquiries',
    avatar: '🤖'
  },
  {
    id: 'bot-2', 
    name: 'Sales Assistant',
    description: 'Assists with product information and sales',
    avatar: '💼'
  },
  {
    id: 'bot-3',
    name: 'Technical Support',
    description: 'Provides technical assistance and troubleshooting',
    avatar: '🔧'
  }
]

const mockScenarios: MockConversationScenario[] = [
  {
    id: 'scenario-1',
    name: 'Customer Support Demo',
    description: 'A conversation with customer support about order status',
    bot: mockBots[0],
    messages: [
      {
        id: 'msg-1',
        content: 'Hello! How can I help you today?',
        isFromUser: false,
        timestamp: new Date(Date.now() - 300000),
        sender: 'Customer Support Bot',
        messageType: 'text'
      },
      {
        id: 'msg-2',
        content: 'Hi, I wanted to check on my order status.',
        isFromUser: true,
        timestamp: new Date(Date.now() - 240000),
        sender: 'User',
        messageType: 'text'
      },
      {
        id: 'msg-3',
        content: 'I\'d be happy to help you check your order status. Could you please provide me with your order number?',
        isFromUser: false,
        timestamp: new Date(Date.now() - 180000),
        sender: 'Customer Support Bot',
        messageType: 'text'
      },
      {
        id: 'msg-4',
        content: 'Sure, my order number is #12345',
        isFromUser: true,
        timestamp: new Date(Date.now() - 120000),
        sender: 'User',
        messageType: 'text'
      },
      {
        id: 'msg-5',
        content: 'Thank you! I found your order #12345. It was shipped yesterday and should arrive within 2-3 business days. You can track it using tracking number TRK789456123.',
        isFromUser: false,
        timestamp: new Date(Date.now() - 60000),
        sender: 'Customer Support Bot',
        messageType: 'text'
      }
    ]
  },
  {
    id: 'scenario-2',
    name: 'Sales Inquiry',
    description: 'Product information and pricing inquiry',
    bot: mockBots[1],
    messages: [
      {
        id: 'msg-6',
        content: 'Welcome! I\'m here to help you find the perfect product. What are you looking for today?',
        isFromUser: false,
        timestamp: new Date(Date.now() - 180000),
        sender: 'Sales Assistant',
        messageType: 'text'
      },
      {
        id: 'msg-7',
        content: 'I\'m interested in your premium subscription plan. What features does it include?',
        isFromUser: true,
        timestamp: new Date(Date.now() - 120000),
        sender: 'User',
        messageType: 'text'
      },
      {
        id: 'msg-8',
        content: 'Great choice! Our premium plan includes unlimited chatbots, advanced analytics, priority support, and custom integrations. It\'s $99/month. Would you like to see a detailed comparison?',
        isFromUser: false,
        timestamp: new Date(Date.now() - 60000),
        sender: 'Sales Assistant',
        messageType: 'text'
      }
    ]
  },
  {
    id: 'scenario-3',
    name: 'New Conversation',
    description: 'Start a fresh conversation',
    bot: mockBots[2],
    messages: [
      {
        id: 'msg-9',
        content: 'Hello! I\'m your technical support assistant. How can I help you today?',
        isFromUser: false,
        timestamp: new Date(),
        sender: 'Technical Support',
        messageType: 'text'
      }
    ]
  }
]

const ChatPage: React.FC = () => {
  const [selectedScenario, setSelectedScenario] = useState<string>('')
  const {
    conversations,
    addConversation,
    setActiveConversation,
    clearConversations,
    activeConversationId
  } = useChatStore()

  useEffect(() => {
    // Load initial scenario if none selected
    if (!selectedScenario && mockScenarios.length > 0) {
      setSelectedScenario(mockScenarios[0].id)
    }
  }, [selectedScenario])

  const loadScenario = (scenarioId: string) => {
    const scenario = mockScenarios.find(s => s.id === scenarioId)
    if (!scenario) return

    setSelectedScenario(scenarioId)

    // Create conversation from scenario
    const conversation: ChatConversation = {
      id: `conv-${scenarioId}`,
      botId: scenario.bot.id,
      sessionId: `session-${Date.now()}`,
      messages: scenario.messages,
      isActive: true,
      startedAt: scenario.messages[0]?.timestamp || new Date(),
      lastActivity: scenario.messages[scenario.messages.length - 1]?.timestamp || new Date()
    }

    // Clear existing conversations and add new one
    clearConversations()
    addConversation(conversation)
    setActiveConversation(conversation.id)
  }

  const clearChat = () => {
    clearConversations()
    setSelectedScenario('')
  }

  const activeConversation = conversations.find(c => c.id === activeConversationId)
  const activeBotName = activeConversation ? 
    mockBots.find(b => b.id === activeConversation.botId)?.name || 'Bot' : 
    'No active conversation'

  return (
    <div style={{ padding: '20px', height: '100vh', display: 'flex', flexDirection: 'column' }}>
      <div style={{ marginBottom: '20px' }}>
        <h1>Chat Interface Demo</h1>
        <p>Test the chat functionality with different scenarios and bots.</p>
      </div>

      {/* Scenario Selector */}
      <div style={{ 
        marginBottom: '20px', 
        padding: '15px', 
        backgroundColor: '#f8f9fa', 
        border: '1px solid #e9ecef',
        borderRadius: '8px',
        display: 'flex',
        gap: '10px',
        alignItems: 'center',
        flexWrap: 'wrap'
      }}>
        <label htmlFor="scenario-select" style={{ fontWeight: 'bold', color: '#343a40' }}>
          Test Scenario:
        </label>
        <select
          id="scenario-select"
          value={selectedScenario}
          onChange={(e) => loadScenario(e.target.value)}
          style={{
            padding: '8px 12px',
            borderRadius: '4px',
            border: '1px solid #ced4da',
            backgroundColor: '#ffffff',
            color: '#495057',
            fontSize: '14px'
          }}
        >
          <option value="">Select a scenario...</option>
          {mockScenarios.map(scenario => (
            <option key={scenario.id} value={scenario.id}>
              {scenario.name} - {scenario.description}
            </option>
          ))}
        </select>
        
        <button
          onClick={clearChat}
          style={{
            padding: '8px 16px',
            backgroundColor: '#dc3545',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '14px',
            fontWeight: '500'
          }}
        >
          Clear Chat
        </button>
      </div>

      {/* Chat Info */}
      {activeConversation && (
        <div style={{ 
          marginBottom: '15px',
          padding: '12px',
          backgroundColor: '#e7f3ff',
          border: '1px solid #b3d9ff',
          borderRadius: '6px',
          fontSize: '14px',
          color: '#1565c0'
        }}>
          <strong>Active Bot:</strong> {activeBotName} | 
          <strong> Conversation ID:</strong> {activeConversation.id} | 
          <strong> Messages:</strong> {activeConversation.messages.length}
        </div>
      )}

      {/* Chat Component */}
      <div style={{ flex: 1, minHeight: '500px' }}>
        {activeConversation ? (
          <Chat 
            conversationId={activeConversation.id}
            height="100%"
            className="demo-chat"
          />
        ) : (
          <div style={{
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#f8f9fa',
            border: '2px dashed #dee2e6',
            borderRadius: '8px',
            color: '#6c757d',
            fontSize: '16px',
            fontWeight: '500'
          }}>
            Select a scenario above to start chatting
          </div>
        )}
      </div>

      {/* Usage Instructions */}
      <div style={{ 
        marginTop: '20px',
        padding: '16px',
        backgroundColor: '#fff8e1',
        border: '1px solid #ffecb3',
        borderRadius: '8px',
        fontSize: '14px',
        color: '#bf8f00'
      }}>
        <h3 style={{ margin: '0 0 12px 0', color: '#bf8f00' }}>Usage Instructions:</h3>
        <ul style={{ margin: 0, paddingLeft: '20px', lineHeight: '1.5' }}>
          <li>Select a scenario from the dropdown to load pre-defined conversations</li>
          <li>Type messages in the input field at the bottom of the chat</li>
          <li>The connection status indicator shows if SignalR is connected</li>
          <li>Bot responses are simulated for demo purposes</li>
          <li>Use "Clear Chat" to reset and start fresh</li>
        </ul>
      </div>
    </div>
  )
}

export default ChatPage