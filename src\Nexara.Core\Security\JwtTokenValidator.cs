using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace Nexara.Core.Security;

public class JwtTokenValidator : ITokenValidator
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<JwtTokenValidator> _logger;
    private readonly JwtSecurityTokenHandler _tokenHandler;
    private readonly SymmetricSecurityKey _signingKey;

    public JwtTokenValidator(IConfiguration configuration, ILogger<JwtTokenValidator> logger)
    {
        _configuration = configuration;
        _logger = logger;
        _tokenHandler = new JwtSecurityTokenHandler();

        var secretKey = _configuration["Jwt:SecretKey"] ?? GenerateSecretKey();
        _signingKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey));
    }

    public Task<EmbedTokenData?> ValidateAsync(string token)
    {
        try
        {
            var tokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = _signingKey,
                ValidateIssuer = true,
                ValidIssuer = _configuration["Jwt:Issuer"] ?? "nexara",
                ValidateAudience = true,
                ValidAudience = _configuration["Jwt:Audience"] ?? "nexara-widget",
                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero
            };

            var principal = _tokenHandler.ValidateToken(token, tokenValidationParameters, out var validatedToken);

            if (validatedToken is not JwtSecurityToken jwtToken)
            {
                return Task.FromResult<EmbedTokenData?>(null);
            }

            var embedDataClaim = principal.FindFirst("embed_data")?.Value;
            if (string.IsNullOrEmpty(embedDataClaim))
            {
                return Task.FromResult<EmbedTokenData?>(null);
            }

            var embedTokenData = JsonSerializer.Deserialize<EmbedTokenData>(embedDataClaim);

            if (embedTokenData?.Expiry < DateTime.UtcNow)
            {
                _logger.LogWarning("Embed token has expired");
                return Task.FromResult<EmbedTokenData?>(null);
            }

            return Task.FromResult(embedTokenData);
        }
        catch (SecurityTokenValidationException ex)
        {
            _logger.LogWarning(ex, "Token validation failed");
            return Task.FromResult<EmbedTokenData?>(null);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating embed token");
            return Task.FromResult<EmbedTokenData?>(null);
        }
    }

    public Task<string> GenerateEmbedTokenAsync(EmbedTokenData tokenData)
    {
        try
        {
            var claims = new[]
            {
                new Claim("embed_data", JsonSerializer.Serialize(tokenData)),
                new Claim("tenant_id", tokenData.TenantId.ToString()),
                new Claim("bot_id", tokenData.BotId.ToString()),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                new Claim(JwtRegisteredClaimNames.Iat, new DateTimeOffset(DateTime.UtcNow).ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64)
            };

            var credentials = new SigningCredentials(_signingKey, SecurityAlgorithms.HmacSha256);

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = tokenData.Expiry,
                SigningCredentials = credentials,
                Issuer = _configuration["Jwt:Issuer"] ?? "nexara",
                Audience = _configuration["Jwt:Audience"] ?? "nexara-widget"
            };

            var token = _tokenHandler.CreateToken(tokenDescriptor);
            return Task.FromResult(_tokenHandler.WriteToken(token));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating embed token");
            throw;
        }
    }

    private static string GenerateSecretKey()
    {
        var key = new byte[32];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(key);
        return Convert.ToBase64String(key);
    }
}
