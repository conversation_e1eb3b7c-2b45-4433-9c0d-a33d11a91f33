using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Nexara.Core.Data;
using Nexara.Core.Models;

namespace Nexara.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class SurveysController : ControllerBase
{
    private readonly NexaraDbContext _context;
    private readonly ILogger<SurveysController> _logger;

    public SurveysController(NexaraDbContext context, ILogger<SurveysController> logger)
    {
        _context = context;
        _logger = logger;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<Survey>>> GetSurveys()
    {
        var surveys = await _context.Surveys
            .Include(s => s.Bot)
            .OrderByDescending(s => s.CreatedAt)
            .ToListAsync();

        return Ok(surveys);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<Survey>> GetSurvey(Guid id)
    {
        var survey = await _context.Surveys
            .Include(s => s.Bot)
            .FirstOrDefaultAsync(s => s.Id == id);

        if (survey == null)
        {
            return NotFound();
        }

        return Ok(survey);
    }

    [HttpPost]
    public async Task<ActionResult<Survey>> CreateSurvey(CreateSurveyRequest request)
    {
        var survey = new Survey
        {
            BotId = request.BotId,
            Name = request.Name,
            TriggerType = request.TriggerType ?? TriggerType.RuleTriggered,
            TriggerConditionJson = request.TriggerConditionJson,
            QuestionsJson = request.QuestionsJson ?? "[]"
        };

        _context.Surveys.Add(survey);
        await _context.SaveChangesAsync();

        _logger.LogInformation("Created survey {SurveyId} for bot {BotId}", survey.Id, survey.BotId);

        return CreatedAtAction(nameof(GetSurvey), new { id = survey.Id }, survey);
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateSurvey(Guid id, UpdateSurveyRequest request)
    {
        var survey = await _context.Surveys.FindAsync(id);
        
        if (survey == null)
        {
            return NotFound();
        }

        survey.Name = request.Name ?? survey.Name;
        survey.TriggerType = request.TriggerType ?? survey.TriggerType;
        survey.TriggerConditionJson = request.TriggerConditionJson ?? survey.TriggerConditionJson;
        survey.QuestionsJson = request.QuestionsJson ?? survey.QuestionsJson;
        survey.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        _logger.LogInformation("Updated survey {SurveyId}", survey.Id);

        return NoContent();
    }

    [HttpPost("{id}/publish")]
    public async Task<IActionResult> PublishSurvey(Guid id)
    {
        var survey = await _context.Surveys.FindAsync(id);
        
        if (survey == null)
        {
            return NotFound();
        }

        survey.Status = SurveyStatus.Published;
        survey.PublishedAt = DateTime.UtcNow;
        survey.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        _logger.LogInformation("Published survey {SurveyId}", survey.Id);

        return NoContent();
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteSurvey(Guid id)
    {
        var survey = await _context.Surveys.FindAsync(id);
        
        if (survey == null)
        {
            return NotFound();
        }

        survey.Status = SurveyStatus.Archived;
        survey.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        _logger.LogInformation("Archived survey {SurveyId}", survey.Id);

        return NoContent();
    }

    private Guid GetTenantId()
    {
        if (HttpContext.Items.TryGetValue("TenantId", out var tenantIdObj) && 
            tenantIdObj is Guid tenantId)
        {
            return tenantId;
        }

        throw new InvalidOperationException("Tenant context not available");
    }
}

public record CreateSurveyRequest(
    string Name, 
    Guid BotId,
    TriggerType? TriggerType = null,
    string? TriggerConditionJson = null,
    string? QuestionsJson = null);

public record UpdateSurveyRequest(
    string? Name = null, 
    TriggerType? TriggerType = null,
    string? TriggerConditionJson = null,
    string? QuestionsJson = null);