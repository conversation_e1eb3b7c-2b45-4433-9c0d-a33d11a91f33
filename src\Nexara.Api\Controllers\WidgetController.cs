using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Nexara.Core.Data;
using Nexara.Core.Models;
using Nexara.Core.Security;

namespace Nexara.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class WidgetController : ControllerBase
{
    private readonly NexaraDbContext _context;
    private readonly ITokenValidator _tokenValidator;
    private readonly IRateLimiter _rateLimiter;
    private readonly ILogger<WidgetController> _logger;

    public WidgetController(
        NexaraDbContext context,
        ITokenValidator tokenValidator,
        IRateLimiter rateLimiter,
        ILogger<WidgetController> logger)
    {
        _context = context;
        _tokenValidator = tokenValidator;
        _rateLimiter = rateLimiter;
        _logger = logger;
    }

    [HttpPost("init")]
    public async Task<IActionResult> InitializeWidget([FromBody] WidgetInitRequest request)
    {
        // Validate embed token
        var tokenData = await _tokenValidator.ValidateAsync(request.Token);
        if (tokenData == null)
        {
            return Unauthorized("Invalid embed token");
        }

        // Check origin
        var origin = Request.Headers["Origin"].ToString();
        if (!string.IsNullOrEmpty(origin) && tokenData.AllowedOrigins.Any() && !tokenData.AllowedOrigins.Contains(origin))
        {
            _logger.LogWarning("Widget initialization from unauthorized origin: {Origin}", origin);
            return Forbid("Origin not authorized for this embed token");
        }

        // Rate limit check
        if (!await _rateLimiter.AllowRequestAsync(request.Token, tokenData.RateLimitPerMinute))
        {
            return StatusCode(429, "Rate limit exceeded for this widget");
        }

        // Verify survey exists and is published
        var survey = await _context.Surveys
            .FirstOrDefaultAsync(s => s.Id == tokenData.SurveyId && s.TenantId == tokenData.TenantId && s.Status == SurveyStatus.Published);

        if (survey == null)
        {
            return NotFound("Survey not found or not published");
        }

        // Set CSP headers to prevent unauthorized embedding
        if (!string.IsNullOrEmpty(origin))
        {
            Response.Headers.Add("Content-Security-Policy",
                $"frame-ancestors {origin} 'self';");
        }

        // Generate session token for SignalR (if needed)
        var sessionToken = await GenerateSessionToken(tokenData, origin);

        var response = new WidgetInitResponse
        {
            SessionToken = sessionToken,
            SignalRUrl = GetSignalRUrl(),
            SurveyId = tokenData.SurveyId,
            Survey = new SurveyInfo
            {
                Id = survey.Id,
                Name = survey.Name,
                Version = survey.Version
            }
        };

        return Ok(response);
    }

    [HttpPost("answer")]
    public async Task<IActionResult> SubmitAnswer(
        [FromHeader(Name = "X-Embed-Token")] string embedToken,
        [FromBody] WidgetAnswerRequest request)
    {
        // Validate embed token
        var tokenData = await _tokenValidator.ValidateAsync(embedToken);
        if (tokenData == null)
        {
            return Unauthorized("Invalid embed token");
        }

        // Rate limit check
        if (!await _rateLimiter.AllowRequestAsync(embedToken, tokenData.RateLimitPerMinute))
        {
            return StatusCode(429, "Rate limit exceeded for this widget");
        }

        // Verify session belongs to the survey in the token
        var session = await _context.Sessions
            .FirstOrDefaultAsync(s => s.Id == request.SessionId && 
                                   s.SurveyId == tokenData.SurveyId && 
                                   s.TenantId == tokenData.TenantId);

        if (session == null)
        {
            return BadRequest("Invalid session");
        }

        // Verify node exists in the survey
        var node = await _context.SurveyNodes
            .FirstOrDefaultAsync(n => n.Id == request.NodeId && n.SurveyId == tokenData.SurveyId);

        if (node == null)
        {
            return BadRequest("Invalid node");
        }

        // Create answer
        var answer = new Answer
        {
            SessionId = request.SessionId,
            NodeId = request.NodeId,
            ValueJson = System.Text.Json.JsonSerializer.Serialize(request.Value),
            Confidence = request.Confidence
        };

        _context.Answers.Add(answer);
        
        // Update session activity
        session.LastActivityAt = DateTime.UtcNow;
        
        await _context.SaveChangesAsync();

        _logger.LogDebug("Widget answer submitted for session {SessionId}, node {NodeId}", 
            request.SessionId, request.NodeId);

        return Ok(new { AnswerId = answer.Id });
    }

    private async Task<string> GenerateSessionToken(EmbedTokenData tokenData, string? origin)
    {
        // For now, just return the embed token
        // In a more sophisticated implementation, you might generate a separate session token
        return await _tokenValidator.GenerateEmbedTokenAsync(tokenData);
    }

    private string GetSignalRUrl()
    {
        // In production, this should come from configuration
        return "https://localhost:7001/chatHub"; // Assuming Realtime service runs on different port
    }
}

public record WidgetInitRequest(string Token);

public record WidgetInitResponse
{
    public required string SessionToken { get; init; }
    public required string SignalRUrl { get; init; }
    public required Guid SurveyId { get; init; }
    public required SurveyInfo Survey { get; init; }
}

public record SurveyInfo
{
    public required Guid Id { get; init; }
    public required string Name { get; init; }
    public required int Version { get; init; }
}

public record WidgetAnswerRequest(Guid SessionId, Guid NodeId, object Value, double? Confidence = null);