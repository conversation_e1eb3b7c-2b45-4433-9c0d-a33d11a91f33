using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Nexara.Core.Data;
using Nexara.Core.Models;
using Nexara.Core.Security;

namespace Nexara.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class WidgetController : ControllerBase
{
    private readonly NexaraDbContext _context;
    private readonly ITokenValidator _tokenValidator;
    private readonly IRateLimiter _rateLimiter;
    private readonly ILogger<WidgetController> _logger;

    public WidgetController(
        NexaraDbContext context,
        ITokenValidator tokenValidator,
        IRateLimiter rateLimiter,
        ILogger<WidgetController> logger)
    {
        _context = context;
        _tokenValidator = tokenValidator;
        _rateLimiter = rateLimiter;
        _logger = logger;
    }

    [HttpPost("init")]
    public async Task<IActionResult> InitializeWidget([FromBody] WidgetInitRequest request)
    {
        // Validate embed token
        var tokenData = await _tokenValidator.ValidateAsync(request.Token);
        if (tokenData == null)
        {
            return Unauthorized("Invalid embed token");
        }

        // Check origin
        var origin = Request.Headers["Origin"].ToString();
        if (!string.IsNullOrEmpty(origin) && tokenData.AllowedOrigins.Any() && !tokenData.AllowedOrigins.Contains(origin))
        {
            _logger.LogWarning("Widget initialization from unauthorized origin: {Origin}", origin);
            return Forbid("Origin not authorized for this embed token");
        }

        // Rate limit check
        if (!await _rateLimiter.AllowRequestAsync(request.Token, tokenData.RateLimitPerMinute))
        {
            return StatusCode(429, "Rate limit exceeded for this widget");
        }

        // Verify bot exists and is active
        var bot = await _context.Bots
            .FirstOrDefaultAsync(b => b.Id == tokenData.BotId && b.TenantId == tokenData.TenantId && b.Status == BotStatus.Active);

        if (bot == null)
        {
            return NotFound("Bot not found or not active");
        }

        // Set CSP headers to prevent unauthorized embedding
        if (!string.IsNullOrEmpty(origin))
        {
            Response.Headers.Add("Content-Security-Policy",
                $"frame-ancestors {origin} 'self';");
        }

        // Generate session token for SignalR (if needed)
        var sessionToken = await GenerateSessionToken(tokenData, origin);

        var response = new WidgetInitResponse
        {
            SessionToken = sessionToken,
            SignalRUrl = GetSignalRUrl(),
            BotId = tokenData.BotId,
            Bot = new BotInfo
            {
                Id = bot.Id,
                Name = bot.Name,
                Personality = bot.Personality
            }
        };

        return Ok(response);
    }

    [HttpPost("message")]
    public async Task<IActionResult> SendMessage(
        [FromHeader(Name = "X-Embed-Token")] string embedToken,
        [FromBody] WidgetMessageRequest request)
    {
        // Validate embed token
        var tokenData = await _tokenValidator.ValidateAsync(embedToken);
        if (tokenData == null)
        {
            return Unauthorized("Invalid embed token");
        }

        // Rate limit check
        if (!await _rateLimiter.AllowRequestAsync(embedToken, tokenData.RateLimitPerMinute))
        {
            return StatusCode(429, "Rate limit exceeded for this widget");
        }

        // Verify conversation belongs to the bot in the token
        var conversation = await _context.Conversations
            .FirstOrDefaultAsync(c => c.Id == request.ConversationId && 
                                   c.BotId == tokenData.BotId && 
                                   c.TenantId == tokenData.TenantId);

        if (conversation == null)
        {
            return BadRequest("Invalid conversation");
        }

        // Create user message
        var message = new Message
        {
            ConversationId = request.ConversationId,
            Role = MessageRole.User,
            Content = request.Content,
            Intent = request.Intent
        };

        _context.Messages.Add(message);
        
        // Update conversation activity
        conversation.LastActivityAt = DateTime.UtcNow;
        
        await _context.SaveChangesAsync();

        _logger.LogDebug("Widget message sent for conversation {ConversationId}", 
            request.ConversationId);

        // TODO: Process message through rules engine and generate response
        var response = new WidgetMessageResponse
        {
            MessageId = message.Id,
            BotResponse = "Thank you for your message. I'm processing your request..."
        };

        return Ok(response);
    }

    private async Task<string> GenerateSessionToken(EmbedTokenData tokenData, string? origin)
    {
        // For now, just return the embed token
        // In a more sophisticated implementation, you might generate a separate session token
        return await _tokenValidator.GenerateEmbedTokenAsync(tokenData);
    }

    private string GetSignalRUrl()
    {
        // In production, this should come from configuration
        return "https://localhost:7001/chatHub"; // Assuming Realtime service runs on different port
    }
}

public record WidgetInitRequest(string Token);

public record WidgetInitResponse
{
    public required string SessionToken { get; init; }
    public required string SignalRUrl { get; init; }
    public required Guid BotId { get; init; }
    public required BotInfo Bot { get; init; }
}

public record BotInfo
{
    public required Guid Id { get; init; }
    public required string Name { get; init; }
    public string? Personality { get; init; }
}

public record WidgetMessageRequest(Guid ConversationId, string Content, string? Intent = null);

public record WidgetMessageResponse
{
    public required Guid MessageId { get; init; }
    public required string BotResponse { get; init; }
    public string? RichContent { get; init; }
}