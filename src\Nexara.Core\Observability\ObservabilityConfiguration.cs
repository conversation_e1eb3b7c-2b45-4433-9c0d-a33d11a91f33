using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using OpenTelemetry.Metrics;
using OpenTelemetry.Trace;
using Serilog;
using Serilog.Formatting.Compact;

namespace Nexara.Core.Observability;

public static class ObservabilityConfiguration
{
    public static IServiceCollection AddObservability(this IServiceCollection services, IConfiguration configuration)
    {
        // Add custom metrics
        services.AddSingleton<RuleMetrics>();
        services.AddSingleton<SessionMetrics>();

        // Configure OpenTelemetry
        services.AddOpenTelemetry()
            .WithTracing(tracing => tracing
                .AddAspNetCoreInstrumentation(options =>
                {
                    options.Filter = (httpContext) => 
                    {
                        // Don't trace health checks
                        return !httpContext.Request.Path.StartsWithSegments("/health");
                    };
                })
                .AddHttpClientInstrumentation()
                .AddSource("Nexara.*")
                .SetSampler(new TraceIdRatioBasedSampler(0.1))) // Sample 10% of traces
            .WithMetrics(metrics => metrics
                .AddAspNetCoreInstrumentation()
                .AddHttpClientInstrumentation()
                .AddMeter("Nexara.Rules")
                .AddMeter("Nexara.Sessions"));

        return services;
    }

    public static IHostBuilder UseSerilog(this IHostBuilder hostBuilder, IConfiguration configuration)
    {
        return hostBuilder.UseSerilog((context, services, config) =>
        {
            config
                .ReadFrom.Configuration(configuration)
                .Enrich.FromLogContext()
                .Enrich.WithProperty("ServiceName", context.HostingEnvironment.ApplicationName)
                .Enrich.WithProperty("Environment", context.HostingEnvironment.EnvironmentName)
                .WriteTo.Console(new CompactJsonFormatter())
                .WriteTo.Conditional(evt => !string.IsNullOrEmpty(configuration["Seq:ServerUrl"]),
                    wt => wt.Seq(configuration["Seq:ServerUrl"]!));
        });
    }
}