using System.ComponentModel.DataAnnotations;

namespace Nexara.Core.Models;

public class User
{
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public Guid TenantId { get; set; }
    
    [Required]
    [MaxLength(100)]
    public required string Role { get; set; }
    
    [Required]
    [EmailAddress]
    [MaxLength(255)]
    public required string Email { get; set; }
    
    [MaxLength(255)]
    public string? Name { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    public virtual Tenant Tenant { get; set; } = null!;
}