using System.ComponentModel.DataAnnotations;

namespace Nexara.Core.Models;

public enum ConversationStatus
{
    Active,
    Completed,
    Abandoned,
    Transferred,
    Expired
}

public class Conversation
{
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public Guid BotId { get; set; }
    
    public Guid TenantId { get; set; }
    
    [MaxLength(255)]
    public string? UserExternalId { get; set; }
    
    public ConversationStatus Status { get; set; } = ConversationStatus.Active;
    
    public DateTime StartedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? CompletedAt { get; set; }
    
    public DateTime LastActivityAt { get; set; } = DateTime.UtcNow;
    
    public string? ContextJson { get; set; }
    
    // Navigation properties
    public virtual Tenant Tenant { get; set; } = null!;
    public virtual Bot Bot { get; set; } = null!;
    public virtual ICollection<ConversationState> ConversationStates { get; set; } = new List<ConversationState>();
    public virtual ICollection<Message> Messages { get; set; } = new List<Message>();
    public virtual ICollection<UserResponse> UserResponses { get; set; } = new List<UserResponse>();
    public virtual ICollection<SurveySession> SurveySessions { get; set; } = new List<SurveySession>();
    public virtual ICollection<Event> Events { get; set; } = new List<Event>();
}