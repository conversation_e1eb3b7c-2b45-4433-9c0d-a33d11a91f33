using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Nexara.Core.Data;

namespace Nexara.Core.MultiTenancy;

public class TenantContextMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ITenantResolver _tenantResolver;
    private readonly ILogger<TenantContextMiddleware> _logger;

    public TenantContextMiddleware(
        RequestDelegate next,
        ITenantResolver tenantResolver,
        ILogger<TenantContextMiddleware> logger)
    {
        _next = next;
        _tenantResolver = tenantResolver;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, ITenantProvider tenantProvider)
    {
        var tenant = await _tenantResolver.ResolveAsync(context);
        
        if (tenant == null)
        {
            _logger.LogWarning("Failed to resolve tenant for request {Path}", context.Request.Path);
            context.Response.StatusCode = 401;
            await context.Response.WriteAsync("Tenant not found");
            return;
        }

        // Set tenant context
        context.Items["Tenant"] = tenant;
        context.Items["TenantId"] = tenant.Id;
        tenantProvider.SetTenantId(tenant.Id);
        tenantProvider.SetTenantName(tenant.Name);

        // Add tenant context to logging scope
        using (_logger.BeginScope(new Dictionary<string, object>
        {
            ["TenantId"] = tenant.Id,
            ["TenantName"] = tenant.Name
        }))
        {
            await _next(context);
        }
    }
}