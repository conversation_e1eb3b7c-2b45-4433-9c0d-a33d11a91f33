using Microsoft.Extensions.DependencyInjection;
using Nexara.Rules.Examples;

namespace Nexara.Rules;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddNexaraRules(this IServiceCollection services)
    {
        services.AddSingleton<IRuleRegistry, RuleRegistry>();
        services.AddSingleton<IRuleEvaluator, RuleEvaluator>();
        
        // Register example rules
        services.AddSingleton<RefundFollowupRule>();
        services.AddSingleton<SatisfactionScoringRule>();

        return services;
    }

    public static IServiceCollection RegisterRule<T>(this IServiceCollection services) 
        where T : class, INexaraRule
    {
        services.AddSingleton<T>();
        return services;
    }
}