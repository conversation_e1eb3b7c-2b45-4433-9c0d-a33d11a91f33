using Microsoft.Extensions.DependencyInjection;
using Nexara.Rules.Examples;

namespace Nexara.Rules;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddNexaraRules(this IServiceCollection services)
    {
        services.AddSingleton<IRuleRegistry, RuleRegistry>();
        services.AddSingleton<IRuleEngine, RuleEngine>();
        
        // Register example rules
        services.AddSingleton<RefundRequestRule>();
        services.AddSingleton<FeedbackTriggerRule>();

        return services;
    }

    public static IServiceCollection RegisterRule<T>(this IServiceCollection services) 
        where T : class, IPromptRule
    {
        services.AddSingleton<T>();
        return services;
    }
}