import { useEffect, useRef, useCallback } from 'react'
import { HubConnection, HubConnectionBuilder, LogLevel } from '@microsoft/signalr'
import { useChatStore } from '../stores/chatStore'
import { 
  SendMessagePayload, 
  ReceiveMessagePayload,
  TypingIndicatorPayload 
} from '../types/chat'

const WS_URL = import.meta.env.VITE_WS_URL || '/chatHub'

export const useChat = () => {
  const connectionRef = useRef<HubConnection | null>(null)
  const {
    isConnected,
    connectionStatus,
    activeConversationId,
    setConnectionStatus,
    setIsConnected,
    addMessage,
    setIsTyping
  } = useChatStore()

  const connect = useCallback(async () => {
    if (connectionRef.current?.state === 'Connected') {
      return
    }

    try {
      setConnectionStatus('connecting')
      
      const connection = new HubConnectionBuilder()
        .withUrl(WS_URL, {
          accessTokenFactory: () => {
            return localStorage.getItem('auth_token') || ''
          }
        })
        .withAutomaticReconnect()
        .configureLogging(LogLevel.Information)
        .build()

      // Event handlers
      connection.on('ReceiveMessage', (payload: ReceiveMessagePayload) => {
        console.log('Received message:', payload)
        addMessage(payload.conversationId, payload.message)
      })

      connection.on('UserTyping', (payload: TypingIndicatorPayload) => {
        console.log('User typing:', payload)
        if (payload.conversationId === activeConversationId) {
          setIsTyping(payload.isTyping)
        }
      })

      connection.on('BotTyping', (payload: TypingIndicatorPayload) => {
        console.log('Bot typing:', payload)
        if (payload.conversationId === activeConversationId) {
          setIsTyping(payload.isTyping)
        }
      })

      connection.onreconnecting(() => {
        setConnectionStatus('connecting')
        setIsConnected(false)
      })

      connection.onreconnected(() => {
        setConnectionStatus('connected')
        setIsConnected(true)
      })

      connection.onclose((error) => {
        setConnectionStatus('disconnected')
        setIsConnected(false)
        if (error) {
          console.error('SignalR connection closed with error:', error)
        }
      })

      await connection.start()
      connectionRef.current = connection
      setConnectionStatus('connected')
      setIsConnected(true)
      
      console.log('SignalR connection established')
    } catch (error) {
      console.error('Failed to connect to SignalR hub:', error)
      setConnectionStatus('error')
      setIsConnected(false)
    }
  }, [setConnectionStatus, setIsConnected, addMessage, setIsTyping, activeConversationId])

  const disconnect = useCallback(async () => {
    if (connectionRef.current) {
      try {
        await connectionRef.current.stop()
        connectionRef.current = null
        setConnectionStatus('disconnected')
        setIsConnected(false)
        console.log('SignalR connection closed')
      } catch (error) {
        console.error('Error closing SignalR connection:', error)
      }
    }
  }, [setConnectionStatus, setIsConnected])

  const sendMessage = useCallback(async (payload: SendMessagePayload) => {
    if (!connectionRef.current || connectionRef.current.state !== 'Connected') {
      throw new Error('Not connected to chat hub')
    }

    try {
      await connectionRef.current.invoke('SendMessage', payload)
      console.log('Message sent:', payload)
    } catch (error) {
      console.error('Failed to send message:', error)
      throw error
    }
  }, [])

  const joinConversation = useCallback(async (conversationId: string) => {
    if (!connectionRef.current || connectionRef.current.state !== 'Connected') {
      throw new Error('Not connected to chat hub')
    }

    try {
      await connectionRef.current.invoke('JoinConversation', conversationId)
      console.log('Joined conversation:', conversationId)
    } catch (error) {
      console.error('Failed to join conversation:', error)
      throw error
    }
  }, [])

  const leaveConversation = useCallback(async (conversationId: string) => {
    if (!connectionRef.current || connectionRef.current.state !== 'Connected') {
      return
    }

    try {
      await connectionRef.current.invoke('LeaveConversation', conversationId)
      console.log('Left conversation:', conversationId)
    } catch (error) {
      console.error('Failed to leave conversation:', error)
    }
  }, [])

  const sendTypingIndicator = useCallback(async (conversationId: string, isTyping: boolean) => {
    if (!connectionRef.current || connectionRef.current.state !== 'Connected') {
      return
    }

    try {
      await connectionRef.current.invoke('SendTypingIndicator', { conversationId, isTyping })
    } catch (error) {
      console.error('Failed to send typing indicator:', error)
    }
  }, [])

  // Auto-connect when hook is used
  useEffect(() => {
    connect()
    
    return () => {
      disconnect()
    }
  }, [connect, disconnect])

  return {
    isConnected,
    connectionStatus,
    connect,
    disconnect,
    sendMessage,
    joinConversation,
    leaveConversation,
    sendTypingIndicator
  }
}