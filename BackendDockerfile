# Build stage
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copy solution and project files
COPY Nexara.sln .
COPY global.json .
COPY src/Nexara.Api/Nexara.Api.csproj src/Nexara.Api/
COPY src/Nexara.Realtime/Nexara.Realtime.csproj src/Nexara.Realtime/
COPY src/Nexara.Core/Nexara.Core.csproj src/Nexara.Core/
COPY src/Nexara.Rules/Nexara.Rules.csproj src/Nexara.Rules/
COPY src/Nexara.Worker/Nexara.Worker.csproj src/Nexara.Worker/
COPY src/Nexara.Connectors/Nexara.Connectors.csproj src/Nexara.Connectors/
COPY src/Nexara.ServiceDefaults/Nexara.ServiceDefaults.csproj src/Nexara.ServiceDefaults/

# Restore dependencies
RUN dotnet restore

# Copy source code
COPY src/ src/

# Build the API project
RUN dotnet build src/Nexara.Api/Nexara.Api.csproj -c Release --no-restore
RUN dotnet publish src/Nexara.Api/Nexara.Api.csproj -c Release --no-build --no-restore -o /app/api

# Build the Realtime project
RUN dotnet build src/Nexara.Realtime/Nexara.Realtime.csproj -c Release --no-restore
RUN dotnet publish src/Nexara.Realtime/Nexara.Realtime.csproj -c Release --no-build --no-restore -o /app/realtime

# Build the Worker project
RUN dotnet build src/Nexara.Worker/Nexara.Worker.csproj -c Release --no-restore
RUN dotnet publish src/Nexara.Worker/Nexara.Worker.csproj -c Release --no-build --no-restore -o /app/worker

# Runtime stage for API
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS api
WORKDIR /app
COPY --from=build /app/api .
EXPOSE 8080
ENTRYPOINT ["dotnet", "Nexara.Api.dll"]

# Runtime stage for Realtime
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS realtime
WORKDIR /app
COPY --from=build /app/realtime .
EXPOSE 8080
ENTRYPOINT ["dotnet", "Nexara.Realtime.dll"]

# Runtime stage for Worker
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS worker
WORKDIR /app
COPY --from=build /app/worker .
ENTRYPOINT ["dotnet", "Nexara.Worker.dll"]

# Default to API service
FROM api AS final