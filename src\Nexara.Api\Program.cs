using Microsoft.EntityFrameworkCore;
using Nexara.Core.Data;
using Nexara.Core.MultiTenancy;
using Nexara.Core.Observability;
using Nexara.Core.Security;
using Nexara.Core.Services;
using Nexara.Rules;

var builder = WebApplication.CreateBuilder(args);

// Add Aspire service defaults
builder.AddServiceDefaults();

// Configure Serilog
builder.Host.UseSerilog(builder.Configuration);

// Add services to the container
builder.Services.AddControllers();
builder.Services.AddOpenApi();

// Configure CORS for frontend
builder.Services.AddCors(options =>
{
    options.AddPolicy("FrontendPolicy", policy =>
    {
        policy.WithOrigins(
                "http://localhost:5173",  // Vite dev server
                "https://localhost:5173", // Vite dev server HTTPS
                "https://*.vercel.app",   // Vercel preview/production domains
                "https://nexara-frontend.vercel.app" // Production frontend (update with actual domain)
            )
            .AllowAnyMethod()
            .AllowAnyHeader()
            .AllowCredentials()
            .SetIsOriginAllowedToAllowWildcardSubdomains();
    });
});

// Add observability
builder.Services.AddObservability(builder.Configuration);

// Database
var connectionString = builder.Configuration.GetConnectionString("DefaultConnection") 
    ?? "Host=localhost;Database=nexara;Username=postgres;Password=password";
builder.Services.AddDbContext<NexaraDbContext>(options =>
    options.UseNpgsql(connectionString));

// Redis Cache
var redisConnectionString = builder.Configuration.GetConnectionString("Redis") 
    ?? "localhost:6379";
builder.Services.AddStackExchangeRedisCache(options =>
{
    options.Configuration = redisConnectionString;
});

// Multi-tenancy
builder.Services.AddScoped<ITenantProvider, TenantProvider>();
builder.Services.AddScoped<ITenantResolver, TenantResolver>();

// Core services
builder.Services.AddScoped<IConversationStateManager, ConversationStateManager>();
builder.Services.AddScoped<IConversationCache, ConversationCache>();
builder.Services.AddScoped<IBotCache, BotCache>();

// Security services
builder.Services.AddScoped<ITokenValidator, JwtTokenValidator>();
builder.Services.AddScoped<IRateLimiter, EmbedTokenRateLimiter>();

// Rules engine
builder.Services.AddNexaraRules();

// Logging
builder.Services.AddLogging();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
    app.UseDeveloperExceptionPage();
}

app.UseHttpsRedirection();

// Enable CORS
app.UseCors("FrontendPolicy");

// Multi-tenancy middleware
app.UseMiddleware<TenantContextMiddleware>();

// Widget security middleware
app.UseMiddleware<WidgetSecurityMiddleware>();

app.UseRouting();
app.MapControllers();

// Map Aspire default endpoints
app.MapDefaultEndpoints();

app.Run();
