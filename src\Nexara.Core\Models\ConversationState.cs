namespace Nexara.Core.Models;

public class ConversationState
{
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public Guid ConversationId { get; set; }
    
    public int Version { get; set; } = 1;
    
    public string StateJson { get; set; } = "{}";
    
    public DateTime LastActivityUtc { get; set; } = DateTime.UtcNow;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    // Navigation properties
    public virtual Conversation Conversation { get; set; } = null!;
}