using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Nexara.Core.Security;

public class WidgetSecurityMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<WidgetSecurityMiddleware> _logger;

    public WidgetSecurityMiddleware(
        RequestDelegate next,
        ILogger<WidgetSecurityMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        if (context.Request.Path.StartsWithSegments("/widget"))
        {
            var token = context.Request.Query["token"].FirstOrDefault()
                ?? context.Request.Headers["X-Embed-Token"].FirstOrDefault();

            if (!string.IsNullOrEmpty(token))
            {
                var tokenValidator = context.RequestServices.GetRequiredService<ITokenValidator>();
                var tokenData = await tokenValidator.ValidateAsync(token);

                if (tokenData != null)
                {
                    // Set CSP headers to only allow embedding on authorized domains
                    var allowedOrigins = string.Join(" ", tokenData.AllowedOrigins);
                    context.Response.Headers["Content-Security-Policy"] =
                        $"frame-ancestors {allowedOrigins} 'self';";

                    // Add additional security headers
                    context.Response.Headers["X-Frame-Options"] = "ALLOWALL";
                    context.Response.Headers["X-Content-Type-Options"] = "nosniff";
                    context.Response.Headers["X-XSS-Protection"] = "1; mode=block";

                    // Store token data in context for later use
                    context.Items["EmbedTokenData"] = tokenData;
                }
                else
                {
                    _logger.LogWarning("Invalid embed token provided for widget request");
                }
            }
        }

        await _next(context);
    }
}
