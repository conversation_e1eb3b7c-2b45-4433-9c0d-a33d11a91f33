using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Nexara.Core.Security;

public class WidgetSecurityMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ITokenValidator _tokenValidator;
    private readonly ILogger<WidgetSecurityMiddleware> _logger;

    public WidgetSecurityMiddleware(
        RequestDelegate next,
        ITokenValidator tokenValidator,
        ILogger<WidgetSecurityMiddleware> logger)
    {
        _next = next;
        _tokenValidator = tokenValidator;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        if (context.Request.Path.StartsWithSegments("/widget"))
        {
            var token = context.Request.Query["token"].FirstOrDefault() 
                ?? context.Request.Headers["X-Embed-Token"].FirstOrDefault();

            if (!string.IsNullOrEmpty(token))
            {
                var tokenData = await _tokenValidator.ValidateAsync(token);
                
                if (tokenData != null)
                {
                    // Set CSP headers to only allow embedding on authorized domains
                    var allowedOrigins = string.Join(" ", tokenData.AllowedOrigins);
                    context.Response.Headers.Add("Content-Security-Policy",
                        $"frame-ancestors {allowedOrigins} 'self';");

                    // Add additional security headers
                    context.Response.Headers.Add("X-Frame-Options", "ALLOWALL");
                    context.Response.Headers.Add("X-Content-Type-Options", "nosniff");
                    context.Response.Headers.Add("X-XSS-Protection", "1; mode=block");

                    // Store token data in context for later use
                    context.Items["EmbedTokenData"] = tokenData;
                }
                else
                {
                    _logger.LogWarning("Invalid embed token provided for widget request");
                }
            }
        }

        await _next(context);
    }
}