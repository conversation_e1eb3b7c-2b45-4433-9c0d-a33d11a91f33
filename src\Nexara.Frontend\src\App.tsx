import { Routes, Route } from 'react-router-dom'
import Dashboard from './pages/Dashboard'
import Bots from './pages/Bots'
import Analytics from './pages/Analytics'
import Settings from './pages/Settings'
import Chat from './pages/Chat'
import Layout from './components/Layout'

function App() {
  return (
    <Layout>
      <Routes>
        <Route path="/" element={<Dashboard />} />
        <Route path="/bots" element={<Bots />} />
        <Route path="/analytics" element={<Analytics />} />
        <Route path="/chat" element={<Chat />} />
        <Route path="/settings" element={<Settings />} />
      </Routes>
    </Layout>
  )
}

export default App