namespace Nexara.Rules.Examples;

public sealed class FeedbackTriggerRule : IPromptRule
{
    public string Key => "feedback-trigger";
    public int Priority => 50;

    public double MatchConfidence(PromptContext context)
    {
        var prompt = context.UserPrompt.ToLowerInvariant();
        
        // Look for feedback/satisfaction keywords
        var feedbackKeywords = new[] { "feedback", "review", "rating", "satisfied", "dissatisfied", "experience", "service", "disappointed", "happy", "terrible", "great", "excellent", "poor" };
        var emotionKeywords = new[] { "love", "hate", "angry", "frustrated", "pleased", "impressed", "awful", "amazing" };
        
        double confidence = 0.0;
        
        // Base confidence if feedback keywords are found
        if (feedbackKeywords.Any(keyword => prompt.Contains(keyword)))
        {
            confidence = 0.6;
        }
        
        // Increase confidence if emotional language is used
        if (emotionKeywords.Any(keyword => prompt.Contains(keyword)))
        {
            confidence += 0.3;
        }
        
        // Boost confidence if it's at end of conversation
        if (context.RecentHistory.Count > 3)
        {
            confidence += 0.1;
        }
        
        return Math.Min(confidence, 1.0);
    }

    public async Task<RuleResponse> GenerateResponseAsync(PromptContext context)
    {
        var prompt = context.UserPrompt.ToLowerInvariant();
        
        // Determine sentiment
        var positiveWords = new[] { "happy", "satisfied", "great", "excellent", "amazing", "love", "pleased", "impressed" };
        var negativeWords = new[] { "dissatisfied", "terrible", "poor", "awful", "hate", "angry", "frustrated", "disappointed" };
        
        var sentiment = "neutral";
        if (positiveWords.Any(word => prompt.Contains(word)))
        {
            sentiment = "positive";
        }
        else if (negativeWords.Any(word => prompt.Contains(word)))
        {
            sentiment = "negative";
        }
        
        var responseText = sentiment switch
        {
            "positive" => "Thank you so much for the positive feedback! It really means a lot to us. Is there anything specific you'd like to share about your experience?",
            "negative" => "I'm sorry to hear about your experience. Your feedback is important to us and we'd like to make this right. Could you tell me more about what went wrong?",
            _ => "Thank you for taking the time to share your thoughts. We value all feedback as it helps us improve. Could you tell me more about your experience?"
        };
        
        var response = new RuleResponse
        {
            Message = responseText,
            AddTags = new List<string> { "feedback_provided", $"sentiment_{sentiment}" },
            UpdateState = new Dictionary<string, object>
            {
                ["intent"] = "feedback_collection",
                ["sentiment"] = sentiment,
                ["feedback_received"] = true
            },
            RichContent = new RichContent
            {
                Type = "quick_replies",
                Options = new[] { "Tell me more", "Leave a review", "Contact support" }
            },
            NextAction = NextAction.WaitForResponse
        };
        
        return await Task.FromResult(response);
    }
}