namespace Nexara.Rules.Examples;

public sealed class SatisfactionScoringRule : INexaraRule
{
    public string Key => "satisfaction-scoring";
    public int Priority => 50;

    public bool Applies(RuleContext ctx)
    {
        return ctx.Answers.ContainsKey("satisfaction_rating");
    }

    public RuleAction Evaluate(RuleContext ctx)
    {
        if (!ctx.Answers.TryGetValue("satisfaction_rating", out var rating) || 
            !int.TryParse(rating?.ToString(), out var numericRating))
        {
            return new RuleAction();
        }

        var scoreDelta = new Dictionary<string, double>();
        var tags = new List<string>();

        switch (numericRating)
        {
            case >= 4:
                scoreDelta["satisfaction"] = 10.0;
                tags.Add("satisfied");
                break;
            case 3:
                scoreDelta["satisfaction"] = 0.0;
                tags.Add("neutral");
                break;
            case <= 2:
                scoreDelta["satisfaction"] = -10.0;
                tags.Add("dissatisfied");
                break;
        }

        return new RuleAction(
            ScoreDelta: scoreDelta,
            AddTags: tags
        );
    }
}