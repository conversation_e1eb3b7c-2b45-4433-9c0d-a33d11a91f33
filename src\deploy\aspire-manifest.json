{"$schema": "https://json.schemastore.org/aspire-8.0.json", "resources": {"postgres": {"type": "container.v0", "connectionString": "Host={postgres.bindings.tcp.host};Port={postgres.bindings.tcp.port};Username=postgres;Password={postgres-password.value}", "image": "docker.io/library/postgres:17.5", "volumes": [{"name": "nexara.apphost-3417b860ec-postgres-data", "target": "/var/lib/postgresql/data", "readOnly": false}], "env": {"POSTGRES_HOST_AUTH_METHOD": "scram-sha-256", "POSTGRES_INITDB_ARGS": "--auth-host=scram-sha-256 --auth-local=scram-sha-256", "POSTGRES_USER": "postgres", "POSTGRES_PASSWORD": "{postgres-password.value}"}, "bindings": {"tcp": {"scheme": "tcp", "protocol": "tcp", "transport": "tcp", "targetPort": 5432}}}, "nexara-db": {"type": "value.v0", "connectionString": "{postgres.connectionString};Database=nexara-db"}, "redis": {"type": "container.v0", "connectionString": "{redis.bindings.tcp.host}:{redis.bindings.tcp.port},password={redis-password.value}", "image": "docker.io/library/redis:7.4", "entrypoint": "/bin/sh", "args": ["-c", "redis-server --requirepass $REDIS_PASSWORD --save 60 1"], "volumes": [{"name": "nexara.apphost-3417b860ec-redis-data", "target": "/data", "readOnly": false}], "env": {"REDIS_PASSWORD": "{redis-password.value}"}, "bindings": {"tcp": {"scheme": "tcp", "protocol": "tcp", "transport": "tcp", "targetPort": 6379}}}, "seq": {"type": "container.v0", "connectionString": "{seq.bindings.http.url}", "image": "docker.io/datalust/seq:2025.1", "env": {"ACCEPT_EULA": "Y"}, "bindings": {"http": {"scheme": "http", "protocol": "tcp", "transport": "http", "targetPort": 80}}}, "nexara-api": {"type": "project.v0", "path": "../Nexara.Api/Nexara.Api.csproj", "env": {"OTEL_DOTNET_EXPERIMENTAL_OTLP_EMIT_EXCEPTION_LOG_ATTRIBUTES": "true", "OTEL_DOTNET_EXPERIMENTAL_OTLP_EMIT_EVENT_LOG_ATTRIBUTES": "true", "OTEL_DOTNET_EXPERIMENTAL_OTLP_RETRY": "in_memory", "ASPNETCORE_FORWARDEDHEADERS_ENABLED": "true", "HTTP_PORTS": "{nexara-api.bindings.http.targetPort}", "ConnectionStrings__nexara-db": "{nexara-db.connectionString}", "ConnectionStrings__redis": "{redis.connectionString}", "Seq__ServerUrl": "{seq.bindings.http.url}"}, "bindings": {"http": {"scheme": "http", "protocol": "tcp", "transport": "http"}, "https": {"scheme": "https", "protocol": "tcp", "transport": "http"}}}, "nexara-realtime": {"type": "project.v0", "path": "../Nexara.Realtime/Nexara.Realtime.csproj", "env": {"OTEL_DOTNET_EXPERIMENTAL_OTLP_EMIT_EXCEPTION_LOG_ATTRIBUTES": "true", "OTEL_DOTNET_EXPERIMENTAL_OTLP_EMIT_EVENT_LOG_ATTRIBUTES": "true", "OTEL_DOTNET_EXPERIMENTAL_OTLP_RETRY": "in_memory", "ASPNETCORE_FORWARDEDHEADERS_ENABLED": "true", "HTTP_PORTS": "{nexara-realtime.bindings.http.targetPort}", "ConnectionStrings__nexara-db": "{nexara-db.connectionString}", "ConnectionStrings__redis": "{redis.connectionString}", "Seq__ServerUrl": "{seq.bindings.http.url}"}, "bindings": {"http": {"scheme": "http", "protocol": "tcp", "transport": "http"}, "https": {"scheme": "https", "protocol": "tcp", "transport": "http"}}}, "nexara-worker": {"type": "project.v0", "path": "../Nexara.Worker/Nexara.Worker.csproj", "env": {"OTEL_DOTNET_EXPERIMENTAL_OTLP_EMIT_EXCEPTION_LOG_ATTRIBUTES": "true", "OTEL_DOTNET_EXPERIMENTAL_OTLP_EMIT_EVENT_LOG_ATTRIBUTES": "true", "OTEL_DOTNET_EXPERIMENTAL_OTLP_RETRY": "in_memory", "ConnectionStrings__nexara-db": "{nexara-db.connectionString}", "ConnectionStrings__redis": "{redis.connectionString}", "Seq__ServerUrl": "{seq.bindings.http.url}"}}, "nexara-frontend": {"type": "executable.v0", "workingDirectory": "../Nexara.Frontend", "command": "npm", "args": ["run", "dev"], "env": {"NODE_ENV": "development", "VITE_API_URL": "{nexara-api.bindings.https.url}", "VITE_WS_URL": "{nexara-realtime.bindings.https.url}", "PORT": "{nexara-frontend.bindings.http.targetPort}"}, "bindings": {"http": {"scheme": "http", "protocol": "tcp", "transport": "http", "port": 5173, "targetPort": 8000, "external": true}}}, "postgres-password": {"type": "parameter.v0", "value": "{postgres-password.inputs.value}", "inputs": {"value": {"type": "string", "secret": true, "default": {"generate": {"minLength": 22}}}}}, "redis-password": {"type": "parameter.v0", "value": "{redis-password.inputs.value}", "inputs": {"value": {"type": "string", "secret": true, "default": {"generate": {"minLength": 22, "special": false}}}}}}}