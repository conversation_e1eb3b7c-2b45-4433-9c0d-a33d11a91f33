/* Custom styles for chat UI with improved contrast and readability */

/* Chat container styling */
.cs-main-container {
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Chat container */
.cs-chat-container {
  background-color: #ffffff;
}

/* Message list */
.cs-message-list {
  background-color: #f8f9fa;
  padding: 16px;
}

/* Individual messages */
.cs-message {
  margin-bottom: 12px;
}

/* Incoming message (bot) styling */
.cs-message--incoming .cs-message__content {
  background-color: #e3f2fd !important;
  border: 1px solid #bbdefb;
  color: #1565c0 !important;
  border-radius: 18px 18px 18px 4px;
}

.cs-message--incoming .cs-message__content-wrapper {
  background-color: transparent !important;
}

.cs-message--incoming .cs-avatar {
  background-color: #2196f3;
  color: white;
}

/* Outgoing message (user) styling */
.cs-message--outgoing .cs-message__content {
  background-color: #1976d2 !important;
  color: #ffffff !important;
  border-radius: 18px 18px 4px 18px;
}

.cs-message--outgoing .cs-message__content-wrapper {
  background-color: transparent !important;
}

/* Message text styling */
.cs-message__text {
  font-size: 14px;
  line-height: 1.4;
  padding: 8px 12px;
  font-weight: 400;
}

/* Sender name styling */
.cs-message__sender {
  font-size: 12px;
  font-weight: 600;
  color: #666666 !important;
  margin-bottom: 4px;
}

/* Timestamp styling */
.cs-message__sent-time {
  font-size: 11px;
  color: #999999 !important;
  margin-top: 4px;
}

/* Message input styling */
.cs-message-input {
  background-color: #ffffff;
  border-top: 1px solid #e0e0e0;
  padding: 12px;
}

.cs-message-input__content-editor {
  background-color: #ffffff !important;
  color: #333333 !important;
  border: 1px solid #d0d0d0 !important;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
}

.cs-message-input__content-editor:focus {
  border-color: #1976d2 !important;
  outline: none;
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

.cs-message-input__content-editor[data-placeholder]:empty::before {
  color: #999999 !important;
  font-style: italic;
}

/* Send button styling */
.cs-button--send {
  background-color: #1976d2 !important;
  color: white !important;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
}

.cs-button--send:hover {
  background-color: #1565c0 !important;
}

.cs-button--send:disabled {
  background-color: #cccccc !important;
  cursor: not-allowed;
}

/* Typing indicator styling */
.cs-typing-indicator {
  background-color: #f0f0f0 !important;
  color: #666666 !important;
  border: 1px solid #e0e0e0;
  border-radius: 18px;
  padding: 8px 12px;
  margin: 8px 0;
}

.cs-typing-indicator__content {
  color: #666666 !important;
  font-size: 13px;
  font-style: italic;
}

/* System messages styling */
.cs-message--system .cs-message__content {
  background-color: #fff3cd !important;
  color: #856404 !important;
  border: 1px solid #ffeaa7;
  border-radius: 12px;
  text-align: center;
  font-size: 13px;
  font-style: italic;
}

/* Custom connection status indicator */
.chat-connection-status {
  position: absolute;
  top: 12px;
  right: 12px;
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chat-connection-status--connected {
  background-color: #4caf50;
  color: white;
}

.chat-connection-status--disconnected {
  background-color: #f44336;
  color: white;
}

.chat-connection-status--connecting {
  background-color: #ff9800;
  color: white;
}

/* Ensure scrollbar has good contrast */
.cs-message-list::-webkit-scrollbar {
  width: 8px;
}

.cs-message-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.cs-message-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.cs-message-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .cs-message__text {
    font-size: 13px;
  }
  
  .cs-message-input__content-editor {
    font-size: 13px;
  }
}