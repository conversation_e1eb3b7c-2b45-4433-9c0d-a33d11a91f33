<Project Sdk="Microsoft.NET.Sdk">

  <Sdk Name="Aspire.AppHost.Sdk" Version="9.4.0" />

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <UserSecretsId>0b4ea563-7256-465f-a00e-de37f1feee4c</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Aspire.Hosting.AppHost" Version="9.4.0" />
    <PackageReference Include="Aspire.Hosting.PostgreSQL" Version="9.4.0" />
    <PackageReference Include="Aspire.Hosting.Redis" Version="9.4.0" />
    <PackageReference Include="Aspire.Hosting.Seq" Version="9.4.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Nexara.Api\Nexara.Api.csproj" />
    <ProjectReference Include="..\Nexara.Realtime\Nexara.Realtime.csproj" />
    <ProjectReference Include="..\Nexara.Worker\Nexara.Worker.csproj" />
  </ItemGroup>

</Project>
