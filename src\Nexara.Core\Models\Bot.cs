using System.ComponentModel.DataAnnotations;

namespace Nexara.Core.Models;

public enum BotStatus
{
    Draft,
    Active,
    Paused,
    Archived
}

public class Bot
{
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public Guid TenantId { get; set; }
    
    [Required]
    [MaxLength(255)]
    public required string Name { get; set; }
    
    [MaxLength(1000)]
    public string? Personality { get; set; }
    
    public string? ConfigJson { get; set; }
    
    public BotStatus Status { get; set; } = BotStatus.Draft;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? DeployedAt { get; set; }
    
    // Navigation properties
    public virtual Tenant Tenant { get; set; } = null!;
    public virtual ICollection<RuleSet> RuleSets { get; set; } = new List<RuleSet>();
    public virtual ICollection<Survey> Surveys { get; set; } = new List<Survey>();
    public virtual ICollection<Conversation> Conversations { get; set; } = new List<Conversation>();
}