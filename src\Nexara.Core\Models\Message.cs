using System.ComponentModel.DataAnnotations;

namespace Nexara.Core.Models;

public enum MessageRole
{
    System,
    User,
    Assistant
}

public enum MessageStatus
{
    Sent,
    Delivered,
    Read,
    Failed
}

public class Message
{
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public Guid SessionId { get; set; }
    
    public MessageRole Role { get; set; }
    
    [Required]
    public required string ContentJson { get; set; }
    
    public MessageStatus Status { get; set; } = MessageStatus.Sent;
    
    public DateTime? DeliveredAt { get; set; }
    
    public DateTime? ReadAt { get; set; }
    
    public string? MetaJson { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public virtual Session Session { get; set; } = null!;
}