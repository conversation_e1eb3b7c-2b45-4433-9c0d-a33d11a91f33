using System.ComponentModel.DataAnnotations;

namespace Nexara.Core.Models;

public enum MessageRole
{
    System,
    User,
    Assistant
}

public enum MessageStatus
{
    Sent,
    Delivered,
    Read,
    Failed
}

public class Message
{
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public Guid ConversationId { get; set; }
    
    public MessageRole Role { get; set; }
    
    [Required]
    public required string Content { get; set; }
    
    public string? Intent { get; set; }
    
    public MessageStatus Status { get; set; } = MessageStatus.Sent;
    
    public DateTime? DeliveredAt { get; set; }
    
    public DateTime? ReadAt { get; set; }
    
    public string? MetaJson { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    // Navigation properties
    public virtual Conversation Conversation { get; set; } = null!;
    public virtual ICollection<UserResponse> UserResponses { get; set; } = new List<UserResponse>();
}