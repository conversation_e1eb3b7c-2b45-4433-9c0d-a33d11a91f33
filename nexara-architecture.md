# Nexara – Architecture & Development Plan (Updated)

**Nexara** is a SaaS platform for chat-based surveys with intelligent follow-up rules, built primarily in **C# (.NET 9)** with **TypeScript** for the web UI components. This plan uses **.NET Aspire** for local development and **Railway** for the initial MVP hosting, with clear options to scale beyond Railway as usage grows.

---

## 1) Goals & Principles
- **Deterministic & auditable** survey flows — rules implemented in **C#** (no DSL in MVP).  
- **Realtime UX** — low-latency message delivery and updates.  
- **Multi-tenant** by design — secure isolation & per-tenant configuration.  
- **Extensible** — straightforward to add integrations, analytics, or an optional DSL later.  
- **Productive developer workflow** — .NET Aspire for local orchestration, typed contracts for FE/BE.  

> **Note on DSL:** The MVP implements rules directly in C#. A future iteration can add a human-readable DSL that compiles to the same rule interfaces for authoring by non-developers.

---

## 2) High-Level Architecture

**Core stack**
- **Backend runtime:** .NET 9 (C#); ASP.NET Core for APIs
- **Realtime:** SignalR (WebSockets; SSE/Long-poll fallback) with Redis backplane
- **Rules:** C# rule classes (deterministic), registered via DI
- **Database:** PostgreSQL (EF Core; per-tenant row filtering/RLS)
- **Cache/State:** Redis (session state, rate limits, compiled surveys/rules cache)
- **Queue/Jobs:** Hangfire for timers & retries; MassTransit (RabbitMQ/ASB) for events
- **Frontends:** 
  - **Admin app:** React + TypeScript (best ecosystem for graph & rule authoring UIs)  
  - **Embeddable widget:** TypeScript Web Component (framework-agnostic, <30KB target)
- **Observability:** OpenTelemetry + Serilog with structured logging contexts
- **Security:** JWT/OIDC, signed embed tokens (short TTL), audit events, tenant context middleware
- **Multi-tenancy:** `tenant_id` on every row + Postgres RLS; EF Core global filters; TenantContextMiddleware

> **Note on tenant isolation:** The initial release uses a single database schema with row-level security. For enterprise customers requiring stronger isolation, future releases will support separate schemas or databases per tenant.

---

## 3) Service Layout

| Service                 | Role                                                                                 |
|-------------------------|--------------------------------------------------------------------------------------|
| **Nexara.Api**          | REST endpoints (surveys, sessions, answers, webhooks, analytics) + auth             |
| **Nexara.Realtime**     | SignalR hub for chat sessions (typing, acks, delivery)                              |
| **Nexara.Orchestrator** | Conversation engine; decides "next node" given session state & rule outcomes        |
| **Nexara.Rules**        | C# rule interfaces & implementations; evaluation utilities                          |
| **Nexara.Worker**       | Background jobs (idle timeouts, reminders, webhook retries, exports, ETL)           |
| **Nexara.Connectors**   | Slack alerts, CRM leads (HubSpot/SFDC), Warehouse sync (BigQuery/Snowflake)         |
| **Nexara.Llm** (opt.)   | Intent/sentiment/entities sidecar; enriches metadata; not in branching path         |

---

## 4) Frontend Components

- **Admin (React + TS)**  
  - Survey graph editor (React Flow), per-node config, versioning & publish  
  - Rule editor (forms for parameters that map to C# rules), preview mode  
  - Analytics dashboards (completion, drop-off per node, time-to-complete)  

- **Embeddable Widget (TS Web Component)**  
  - Lightweight, themeable, accessible (ARIA)  
  - Connects to SignalR; renders messages & inputs; logs events  

---

## 5) Data Model (Core Entities)

- `Tenant(id, name, plan, settingsJson)`  
- `User(id, tenantId, role, email, ...)`  
- `Survey(id, tenantId, version, status, metaJson)`  
- `SurveyNode(id, surveyId, type, contentJson, uiSchemaJson)`  // question | message | end  
- `SurveyEdge(id, fromNodeId, toNodeId, ruleKey)`              // key to an associated rule  
- `RuleDefinition(id, surveyId, ruleKey, ruleType, definitionJson, compiledAssembly, version)` // for future DSL migration
- `Session(id, surveyId, tenantId, userExternalId, status, startedAt, ...)`  
- `SessionState(id, sessionId, version, answersJson, navigationHistoryJson, lastActivityUtc)` // versioned state
- `Message(id, sessionId, role, contentJson, status, deliveredAt, readAt, metaJson, createdAt)` // with delivery tracking
- `Answer(id, sessionId, nodeId, valueJson, confidence, createdAt)`  
- `Event(id, sessionId, type, payloadJson, ts)`                // append-only for analytics  

**Notes**  
- `ruleKey` references a rule in C# (e.g., registered by name/type via DI).  
- `RuleDefinition` table prepares for future DSL support without breaking changes.
- `SessionState` enables concurrent update handling and back navigation support.
- `Message.status` tracks delivery lifecycle (Sent, Delivered, Read).
- Historical sessions keep the survey version they started with.  
- Sensitive fields can use field-level encryption; audit events are immutable.

---

## 6) Rules in C# (MVP)

**Concept:** Rules are implemented as C# classes behind a stable interface. This keeps branching **deterministic and testable** without a DSL. Later, a DSL can compile into rule instances that implement the same interfaces.

```csharp
public interface INexaraRule
{
    string Key { get; } // e.g., "refund-followup"
    int Priority { get; } // higher runs first for first-match policy
    bool Applies(RuleContext ctx); // pure predicate
    RuleAction Evaluate(RuleContext ctx); // returns next node, tags, scores, events
}

public sealed class RuleContext
{
    public required Guid SessionId { get; init; }
    public required string Locale { get; init; }
    public required IReadOnlyDictionary<string, object?> Answers { get; init; }
    public required IDictionary<string, double> Scores { get; init; }
    public required ISet<string> Tags { get; init; }
    public required IDictionary<string, object?> Meta { get; init; } // age, plan, device...
}

public sealed record RuleAction(
    string? GotoNodeId = null,
    IReadOnlyDictionary<string, double>? ScoreDelta = null,
    IReadOnlyCollection<string>? AddTags = null,
    string? EndMessage = null,
    object? EmitEvent = null
);

// Enhanced rule evaluation with audit trail
public interface IRuleEvaluator
{
    Task<RuleEvaluationResult> EvaluateAsync(
        IEnumerable<INexaraRule> rules, 
        RuleContext context,
        CancellationToken ct);
}

public class RuleEvaluationResult
{
    public string? NextNodeId { get; init; }
    public IReadOnlyList<RuleMatch> MatchedRules { get; init; }
    public TimeSpan EvaluationTime { get; init; }
    public DateTime EvaluatedAt { get; init; }
    // Built-in audit trail
}

public class RuleMatch
{
    public string RuleKey { get; init; }
    public int Priority { get; init; }
    public RuleAction Action { get; init; }
    public bool Applied { get; init; }
}
```

**Example rule (C#):**

```csharp
public sealed class RefundFollowupRule : INexaraRule
{
    public string Key => "refund-followup";
    public int Priority => 80;

    public bool Applies(RuleContext ctx)
    {
        var q1 = ctx.Answers.TryGetValue("q1", out var a1) ? a1 as string : null;
        var feedback = ctx.Answers.TryGetValue("feedback", out var fb) ? fb?.ToString() : null;
        var age = ctx.Meta.TryGetValue("age", out var mAge) && double.TryParse(mAge?.ToString(), out var nAge) ? nAge : (double?)null;

        return q1 == "Yes"
            && (feedback?.IndexOf("refund", StringComparison.OrdinalIgnoreCase) >= 0)
            && age is > 34;
    }

    public RuleAction Evaluate(RuleContext ctx) => new(GotoNodeId: "q_refund_reason");
}
```

**Evaluation strategy:**  
- Rules are resolved by **tenant+survey** and ordered by `Priority` (desc), then registration order.  
- Default policy: **first-match** (stop after the first rule fires).  
- Alternate policy: **all-match** (accumulate `ScoreDelta`, `AddTags`, final `GotoNodeId` from highest priority).  
- Each evaluation produces an **audit record** (matched rule keys, inputs, outputs) via `RuleEvaluationResult`.

> **Future DSL:** A later iteration can add a readable DSL that compiles into `INexaraRule` implementations (same semantics, same tests).

---

## 7) Session State Management

**Versioned state for concurrent updates and navigation:**

```csharp
public class SessionState
{
    public int Version { get; set; } // Optimistic concurrency control
    public Guid SessionId { get; set; }
    public Dictionary<string, object?> Answers { get; set; }
    public Stack<string> NavigationHistory { get; set; } // Enables back button
    public DateTime LastActivityUtc { get; set; }
    public Dictionary<string, double> Scores { get; set; }
    public HashSet<string> Tags { get; set; }
}

public interface ISessionStateManager
{
    Task<SessionState> GetStateAsync(Guid sessionId);
    Task<SessionState> UpdateStateAsync(SessionState state); // Handles version conflicts
    Task<bool> TryNavigateBackAsync(Guid sessionId);
}
```

---

## 8) Caching Strategy

**Explicit caching for performance:**

```csharp
public interface ISurveyCache
{
    Task<Survey?> GetSurveyAsync(Guid surveyId, int version);
    Task<IReadOnlyList<INexaraRule>> GetCompiledRulesAsync(Guid surveyId);
    Task InvalidateSurveyAsync(Guid surveyId);
}

public interface ISessionCache
{
    Task<SessionState?> GetSessionStateAsync(Guid sessionId);
    Task SetSessionStateAsync(Guid sessionId, SessionState state, TimeSpan ttl);
}

// Implementation uses Redis with appropriate serialization
public class RedisSurveyCache : ISurveyCache
{
    private readonly IConnectionMultiplexer _redis;
    private readonly IDatabase _db;
    
    public async Task<Survey?> GetSurveyAsync(Guid surveyId, int version)
    {
        var key = $"survey:{surveyId}:v{version}";
        // Get from Redis, fallback to DB, cache on miss
    }
}
```

---

## 9) Multi-tenancy & Security

### Tenant Isolation

**Tenant isolation middleware (even with single schema):**

```csharp
public class TenantContextMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ITenantResolver _tenantResolver;
    private readonly ILogger<TenantContextMiddleware> _logger;

    public async Task InvokeAsync(HttpContext context)
    {
        var tenant = await _tenantResolver.ResolveAsync(context);
        if (tenant == null)
        {
            _logger.LogWarning("Failed to resolve tenant for request {Path}", context.Request.Path);
            context.Response.StatusCode = 401;
            return;
        }
        
        context.Items["Tenant"] = tenant;
        context.Items["TenantId"] = tenant.Id;
        
        // Set up tenant-scoped logging context
        using (_logger.BeginScope(new Dictionary<string, object>
        {
            ["TenantId"] = tenant.Id,
            ["TenantName"] = tenant.Name
        }))
        {
            await _next(context);
        }
    }
}

// EF Core configuration for automatic filtering
public class NexaraDbContext : DbContext
{
    private readonly Guid _tenantId;
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Global query filter for all tenant-scoped entities
        modelBuilder.Entity<Survey>().HasQueryFilter(s => s.TenantId == _tenantId);
        modelBuilder.Entity<Session>().HasQueryFilter(s => s.TenantId == _tenantId);
        // ... etc
    }
}
```

### Widget Security

**Embed token structure and validation:**

```csharp
// Embed token payload (JWT)
public class EmbedTokenData
{
    public Guid TenantId { get; set; }
    public Guid SurveyId { get; set; }
    public List<string> AllowedOrigins { get; set; } // e.g., ["https://customer.com"]
    public DateTime Expiry { get; set; } // Short TTL (1 hour)
    public int RateLimitPerMinute { get; set; } = 100;
}

// Widget initialization with security
[HttpPost("widget/init")]
public async Task<IActionResult> InitializeWidget([FromBody] WidgetInitRequest request)
{
    // Validate embed token
    var tokenData = await _tokenValidator.ValidateAsync(request.Token);
    if (tokenData == null)
        return Unauthorized();
    
    // Check origin
    var origin = Request.Headers["Origin"].ToString();
    if (!tokenData.AllowedOrigins.Contains(origin))
        return Forbid();
    
    // Rate limit check
    if (!await _rateLimiter.AllowRequestAsync(request.Token))
        return StatusCode(429);
    
    // Set CSP headers to prevent unauthorized embedding
    Response.Headers.Add("Content-Security-Policy", 
        $"frame-ancestors {origin} 'self';");
    
    // Generate session token for SignalR
    var sessionToken = GenerateSessionToken(tokenData, origin);
    
    return Ok(new { sessionToken, signalRUrl, surveyId = tokenData.SurveyId });
}
```

**Content Security Policy (CSP) implementation:**

```csharp
public class WidgetSecurityMiddleware
{
    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        if (context.Request.Path.StartsWithSegments("/widget"))
        {
            // Decode embed token to get allowed origins
            var token = context.Request.Query["token"];
            var tokenData = await _tokenValidator.ValidateAsync(token);
            
            if (tokenData != null)
            {
                // Set CSP to only allow embedding on authorized domains
                var allowedOrigins = string.Join(" ", tokenData.AllowedOrigins);
                context.Response.Headers.Add("Content-Security-Policy",
                    $"frame-ancestors {allowedOrigins} 'self';");
            }
        }
        await next();
    }
}
```

**SignalR origin validation:**

```csharp
public class ChatHub : Hub
{
    private readonly ITokenValidator _tokenValidator;
    private readonly ILogger<ChatHub> _logger;
    
    public override async Task OnConnectedAsync()
    {
        var httpContext = Context.GetHttpContext();
        var origin = httpContext.Request.Headers["Origin"].ToString();
        var token = httpContext.Request.Query["token"];
        
        // Validate token
        var tokenData = await _tokenValidator.ValidateAsync(token);
        if (tokenData == null)
        {
            Context.Abort();
            return;
        }
        
        // Validate origin matches allowed origins in token
        if (!tokenData.AllowedOrigins.Contains(origin))
        {
            _logger.LogWarning("SignalR connection from unauthorized origin: {Origin}", origin);
            Context.Abort();
            return;
        }
        
        // Store validated context
        Context.Items["TenantId"] = tokenData.TenantId;
        Context.Items["SurveyId"] = tokenData.SurveyId;
        Context.Items["Origin"] = origin;
        
        // Add to survey group
        await Groups.AddToGroupAsync(Context.ConnectionId, $"survey-{tokenData.SurveyId}");
        
        await base.OnConnectedAsync();
    }
}

// CORS configuration for SignalR
builder.Services.AddCors(options =>
{
    options.AddPolicy("WidgetCors", policy =>
    {
        policy.SetIsOriginAllowed(origin => IsOriginAllowedForAnyCustomer(origin))
              .AllowCredentials()
              .AllowAnyHeader()
              .AllowAnyMethod();
    });
});
```

**Rate limiting per embed token:**

```csharp
public class EmbedTokenRateLimiter
{
    private readonly IDistributedCache _cache;
    
    public async Task<bool> AllowRequestAsync(string token, int limitPerMinute = 100)
    {
        var tokenHash = ComputeHash(token); // Don't store raw tokens
        var key = $"rate_limit:embed:{tokenHash}";
        
        var count = await _cache.GetStringAsync(key);
        if (count == null)
        {
            await _cache.SetStringAsync(key, "1", new DistributedCacheEntryOptions
            {
                SlidingExpiration = TimeSpan.FromMinutes(1)
            });
            return true;
        }
        
        var currentCount = int.Parse(count);
        if (currentCount >= limitPerMinute)
        {
            return false;
        }
        
        await _cache.IncrementAsync(key);
        return true;
    }
}

// Apply rate limiting in controllers
[ApiController]
public class WidgetApiController : ControllerBase
{
    [HttpPost("widget/answer")]
    public async Task<IActionResult> SubmitAnswer(
        [FromHeader(Name = "X-Embed-Token")] string embedToken, 
        [FromBody] AnswerRequest request)
    {
        if (!await _rateLimiter.AllowRequestAsync(embedToken))
        {
            return StatusCode(429, "Rate limit exceeded for this widget");
        }
        
        // Process answer...
    }
}
```

---

## 10) Observability & Monitoring

**Structured logging with correlation:**

```csharp
public class ObservabilityConfiguration
{
    public static void Configure(WebApplicationBuilder builder)
    {
        // OpenTelemetry
        builder.Services.AddOpenTelemetry()
            .WithTracing(tracing => tracing
                .AddAspNetCoreInstrumentation()
                .AddHttpClientInstrumentation()
                .AddSqlClientInstrumentation()
                .AddSource("Nexara.*"))
            .WithMetrics(metrics => metrics
                .AddAspNetCoreInstrumentation()
                .AddMeter("Nexara.Rules")
                .AddMeter("Nexara.Sessions"));

        // Serilog with structured contexts
        builder.Host.UseSerilog((context, config) =>
        {
            config
                .Enrich.FromLogContext()
                .Enrich.WithProperty("ServiceName", context.HostingEnvironment.ApplicationName)
                .Enrich.WithProperty("Environment", context.HostingEnvironment.EnvironmentName)
                .Enrich.WithCorrelationId()
                .WriteTo.Console(new JsonFormatter())
                .WriteTo.Seq(context.Configuration["Seq:ServerUrl"]);
        });
    }
}

// Custom metrics for rules
public class RuleMetrics
{
    private readonly Meter _meter;
    private readonly Histogram<double> _evaluationTime;
    private readonly Counter<long> _ruleMatches;

    public RuleMetrics()
    {
        _meter = new Meter("Nexara.Rules");
        _evaluationTime = _meter.CreateHistogram<double>("rule.evaluation.time", "ms");
        _ruleMatches = _meter.CreateCounter<long>("rule.matches");
    }

    public void RecordEvaluation(string ruleKey, double milliseconds, bool matched)
    {
        _evaluationTime.Record(milliseconds, new("rule", ruleKey));
        if (matched) _ruleMatches.Add(1, new("rule", ruleKey));
    }
}
```

---

## 11) SignalR Configuration

**Redis backplane for horizontal scaling (even in Railway):**

```csharp
// In Program.cs
var redisConnection = builder.Configuration.GetConnectionString("Redis");

builder.Services.AddSignalR(options =>
{
    options.EnableDetailedErrors = builder.Environment.IsDevelopment();
    options.KeepAliveInterval = TimeSpan.FromSeconds(15);
})
.AddStackExchangeRedis(redisConnection, options => 
{
    options.Configuration.ChannelPrefix = $"nexara_{builder.Environment.EnvironmentName}";
});

// Message delivery acknowledgments
public class ChatHub : Hub
{
    public async Task SendMessage(string sessionId, ChatMessage message)
    {
        message.Status = MessageStatus.Sent;
        await _messageService.SaveMessageAsync(message);
        
        await Clients.Group(sessionId).SendAsync("ReceiveMessage", message);
    }
    
    public async Task AckMessage(string messageId)
    {
        await _messageService.UpdateStatusAsync(messageId, MessageStatus.Delivered);
    }
    
    public async Task MarkMessageRead(string messageId)
    {
        await _messageService.UpdateStatusAsync(messageId, MessageStatus.Read);
    }
}
```

---

## 12) Testing Strategy

**Comprehensive testing approach:**

```csharp
// Unit tests for rules
[TestClass]
public class RefundFollowupRuleTests
{
    [TestMethod]
    public void Should_Apply_When_Conditions_Met()
    {
        var rule = new RefundFollowupRule();
        var context = new RuleContext
        {
            Answers = new Dictionary<string, object?> 
            { 
                ["q1"] = "Yes",
                ["feedback"] = "I want a refund please"
            },
            Meta = new Dictionary<string, object?> { ["age"] = 35 }
        };
        
        Assert.IsTrue(rule.Applies(context));
    }
}

// Property-based testing for rules
[TestClass]
public class RulePropertyTests
{
    [TestMethod]
    public void Rules_Should_Be_Deterministic()
    {
        Prop.ForAll<RuleContext>(context =>
        {
            var rule = new RefundFollowupRule();
            var result1 = rule.Applies(context);
            var result2 = rule.Applies(context);
            return result1 == result2;
        }).QuickCheckThrowOnFailure();
    }
}

// Integration tests
[TestClass]
public class SessionIntegrationTests
{
    [TestMethod]
    public async Task Complete_Survey_Flow()
    {
        // Test full survey completion with rule evaluation
        var client = _factory.CreateClient();
        var sessionId = await StartSession(client);
        
        await AnswerQuestion(client, sessionId, "q1", "Yes");
        await AnswerQuestion(client, sessionId, "feedback", "refund issue");
        
        var nextNode = await GetNextNode(client, sessionId);
        Assert.AreEqual("q_refund_reason", nextNode.Id);
    }
}

// Load testing for SignalR
public class SignalRLoadTest
{
    [Test]
    public async Task Handle_1000_Concurrent_Connections()
    {
        var connections = new List<HubConnection>();
        // Create and connect 1000 clients
        // Assert message delivery under load
    }
}
```

---

## 13) Development Environment – .NET Aspire

**Why Aspire?** One-command orchestration for all services + infra with a rich dashboard (logs/traces/health).

**AppHost composition (example):**
- Infra: Postgres (`WithDataVolume()`), Redis, Seq (logs), OTLP collector (traces)
- Services: `Nexara.Api`, `Nexara.Realtime`, `Nexara.Worker`
- Optionally: run Admin (Vite) as an executable for local preview

**Daily workflow:**
```bash
dotnet run -p src/Nexara.AppHost
# Aspire dashboard URL is printed to console
```

---

## 14) Hosting & Deployment

### MVP (Railway-first)
- **Platform:** Railway for **Nexara.Api**, **Nexara.Realtime**, **Nexara.Worker** (as separate services).  
- **Data:** Railway Postgres & Redis (or Neon Postgres + Railway/Upstash Redis).  
- **Domains:** `api.nexara.com`, `realtime.nexara.com` pointing to Railway services.  
- **Frontends:** Vercel for Admin & widget assets (global CDN, preview deploys).  
- **CI/CD:** GitHub Actions → build & push Docker images → Railway deploy; Vercel deploy for frontends.

### Scaling beyond Railway
- **More throughput (single region):** Raise Railway plan, tune process counts, add Redis & DB connection pools; split Worker to its own plan.  
- **Global low-latency realtime:** Move API/Realtime to **Fly.io** in 2–3 regions (e.g., iad/lhr/syd) with Redis backplane near hubs; keep Postgres serverless (Neon) or managed with read replicas.  
- **Enterprise & Azure alignment:** Migrate services to **Azure Container Apps** and use **Azure SignalR Service**; keep Postgres on Azure Database for PostgreSQL and Redis on Azure Cache.  
- **Data warehouse:** Add BigQuery/Snowflake sync via periodic Worker jobs.  
- **High availability:** Introduce health-check based rollouts, blue/green, and cross-region failover (DNS + active/active).

---

## 15) Dev → Prod Flow
1. **Contracts:** OpenAPI → NSwag generates C# and TypeScript clients.  
2. **Local dev:** Aspire runs full stack; seed DB with demo surveys.  
3. **Tests:** Unit (rules/orchestrator), integration (API/DB), E2E (Playwright: complete a seeded survey), property-based (FsCheck).  
4. **CI/CD:** Actions build, test, publish images; deploy to Railway (per service).  
5. **Monitoring:** OTEL traces + Serilog → Seq; custom metrics for rule evaluation; alerts from APM/log platform.

---

## 16) Milestones

**Week 1**  
- EF Core schema + RLS; seed data & migrations  
- OpenAPI contracts; NSwag C# & TS clients  
- Aspire AppHost wiring (Postgres, Redis, Seq, OTEL)
- TenantContextMiddleware + EF Core global filters

**Week 2**  
- C# rules interfaces + IRuleEvaluator + initial rule set  
- SessionState management with versioning
- Orchestrator core logic + audit trail via RuleEvaluationResult
- API endpoints (sessions, messages, surveys) + happy-path E2E

**Week 3**  
- SignalR realtime service with Redis backplane; reconnect & acks  
- Message delivery tracking (Sent/Delivered/Read)
- Widget v1 (TS Web Component); Admin skeleton (React Flow, rule forms)
- Caching layer (ISurveyCache, ISessionCache)

**Week 4**  
- Worker jobs (idle, reminders, webhook retries)  
- Basic analytics with custom metrics; Slack connector; Railway deployment  
- Property-based tests for rules; load tests for SignalR
- **MVP Ready** – publish survey, embed widget, deterministic follow-ups

**Weeks 5–6**  
- CRM connectors, warehouse sync  
- PII encryption, data retention, feature flags  
- Enhanced observability dashboards
- v1.0 launch & scale plan refinement

---

## 17) Acceptance Criteria (MVP)
- Given answers `{ q1: "Yes", feedback: "refund...", age: 37 }`, the **RefundFollowupRule** routes to `q_refund_reason`.  
- Widget connects via SignalR, receives first node <300ms p95 (local/dev), and completes a seeded survey.  
- Message delivery tracking shows Sent → Delivered → Read lifecycle.
- `session.completed` webhook is delivered with signature; retry on 5xx.  
- Admin Preview reproduces the widget flow using the same API/Realtime.
- TenantContextMiddleware validates all requests; EF Core filters apply automatically.
- Rule evaluation produces audit trail with timing metrics.
- Widget security enforces:
  - CSP headers prevent embedding on unauthorized domains
  - SignalR connections reject invalid origins
  - Rate limiting prevents abuse (100 requests/minute per embed token)

---

## 18) Future Enhancements
- Human-readable **DSL** that compiles into `INexaraRule` implementations (using RuleDefinition table).  
- A/B testing of nodes and copy variants.  
- In-app path analytics & funnel explorer.  
- Advanced quotas and per-tenant throttling.  
- Multi-region active/active rollout with traffic steering.
- **Enterprise tenant isolation** with separate schemas or databases per customer.
- Back navigation support using SessionState.NavigationHistory.