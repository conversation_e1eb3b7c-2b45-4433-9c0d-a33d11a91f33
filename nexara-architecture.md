# Nexara – Architecture & Development Plan (Updated)

**Nexara** is a SaaS platform for intelligent chatbots with rule-based responses and integrated survey capabilities, built primarily in **C# (.NET 9)** with **TypeScript** for the web UI components. This plan uses **.NET Aspire** for local development and **Railway** for the initial MVP hosting, with clear options to scale beyond Railway as usage grows.

---

## 1) Goals & Principles
- **Flexible chatbot platform** — supports both conversational AI and structured surveys.
- **Rule-based responses** — deterministic rules implemented in **C#** for matching prompts and generating responses.
- **Hybrid interaction modes** — can respond to user prompts OR proactively initiate surveys.
- **Realtime UX** — low-latency message delivery and updates.
- **Multi-tenant** by design — secure isolation & per-tenant configuration.
- **Extensible** — straightforward to add integrations, analytics, or LLM augmentation.
- **Productive developer workflow** — .NET Aspire for local orchestration, typed contracts for FE/BE.

> **Note on Rules:** The MVP implements rules directly in C# for both response matching and conversation flow. A future iteration can add a human-readable DSL that compiles to the same rule interfaces.

---

## 2) High-Level Architecture

**Core stack**
- **Backend runtime:** .NET 9 (C#); ASP.NET Core for APIs
- **Realtime:** SignalR (WebSockets; SSE/Long-poll fallback) with Redis backplane
- **Rules:** C# rule classes for prompt matching & response generation
- **Database:** PostgreSQL (EF Core; per-tenant row filtering/RLS)
- **Cache/State:** Redis (session state, rate limits, compiled rules cache)
- **Queue/Jobs:** Hangfire for scheduled surveys & background tasks
- **Frontends:** 
  - **Admin app:** React + TypeScript (bot configuration, rule management, analytics)
  - **Embeddable widget:** TypeScript Web Component (framework-agnostic, <30KB target)
- **Observability:** OpenTelemetry + Serilog with structured logging contexts
- **Security:** JWT/OIDC, signed embed tokens (short TTL), audit events, tenant context middleware
- **Multi-tenancy:** `tenant_id` on every row + Postgres RLS; EF Core global filters; TenantContextMiddleware

> **Note on tenant isolation:** The initial release uses a single database schema with row-level security. For enterprise customers requiring stronger isolation, future releases will support separate schemas or databases per tenant.

---

## 3) Service Layout

| Service                 | Role                                                                                 |
|-------------------------|--------------------------------------------------------------------------------------|
| **Nexara.Api**          | REST endpoints (bots, conversations, rules, webhooks, analytics) + auth             |
| **Nexara.Realtime**     | SignalR hub for chat sessions (typing, acks, delivery)                              |
| **Nexara.ChatEngine**   | Core conversation engine; matches prompts to rules, manages conversation flow       |
| **Nexara.Rules**        | C# rule interfaces & implementations for prompt matching and responses              |
| **Nexara.Worker**       | Background jobs (scheduled surveys, idle timeouts, webhook retries, exports)        |
| **Nexara.Connectors**   | Slack/Teams integration, CRM sync (HubSpot/SFDC), Webhook delivery                 |
| **Nexara.Llm** (opt.)   | LLM fallback for unmatched prompts; intent enrichment (not primary flow)           |

---

## 4) Frontend Components

- **Admin (React + TS)**  
  - Bot configuration (personality, default responses, behavior settings)
  - Rule editor (C# code editor with syntax highlighting, validation)
  - Survey designer (question flows, trigger conditions)
  - Conversation analytics (usage patterns, rule match rates, survey completion)
  - Testing console (interactive bot testing)

- **Embeddable Widget (TS Web Component)**  
  - Lightweight, themeable, accessible (ARIA)
  - Connects to SignalR; renders messages & inputs
  - Supports rich content (buttons, cards, media)

---

## 5) Data Model (Core Entities)

- `Tenant(id, name, plan, settingsJson)`
- `Bot(id, tenantId, name, personality, configJson, status)` // Bot configuration
- `RuleSet(id, botId, name, version, isActive)` // Container for rules
- `PromptRule(id, ruleSetId, key, priority, matchPattern, responseTemplate, actionType)` // Prompt matching rules
- `Survey(id, botId, name, triggerType, triggerCondition, questionsJson)` // Survey definitions
- `Conversation(id, botId, tenantId, userExternalId, status, startedAt, context)`
- `ConversationState(id, conversationId, version, stateJson, lastActivityUtc)` // Versioned state
- `Message(id, conversationId, role, content, intent, status, deliveredAt, readAt, metaJson)`
- `UserResponse(id, conversationId, messageId, responseJson, timestamp)`
- `SurveySession(id, conversationId, surveyId, status, startedAt, completedAt, answersJson)`
- `Event(id, conversationId, type, payloadJson, timestamp)` // Audit trail

**Notes**
- `PromptRule` defines how the bot responds to specific patterns/intents
- `Survey` can be triggered by rules, scheduled, or initiated proactively
- `ConversationState` maintains context across messages with versioning
- Sensitive fields use field-level encryption; audit events are immutable

---

## 6) Rules System (C#)

**Concept:** Rules handle both prompt matching and response generation. They can trigger surveys, provide answers, or execute actions.

```csharp
public interface IPromptRule
{
    string Key { get; }
    int Priority { get; } // Higher priority rules match first
    double MatchConfidence(PromptContext context); // 0.0 to 1.0
    Task<RuleResponse> GenerateResponseAsync(PromptContext context);
}

public class PromptContext
{
    public required string UserPrompt { get; init; }
    public required Guid ConversationId { get; init; }
    public required ConversationState State { get; init; }
    public required Dictionary<string, object> UserAttributes { get; init; }
    public required List<Message> RecentHistory { get; init; } // Last N messages
    public required Bot BotConfig { get; init; }
}

public class RuleResponse
{
    public required string Message { get; init; }
    public RichContent? RichContent { get; init; } // Buttons, cards, etc.
    public string? InitiateSurveyId { get; init; }
    public Dictionary<string, object>? UpdateState { get; init; }
    public List<string>? AddTags { get; init; }
    public WebhookTrigger? TriggerWebhook { get; init; }
    public NextAction? NextAction { get; init; }
}

public enum NextAction
{
    WaitForResponse,
    EndConversation,
    TransferToHuman,
    ScheduleFollowup
}

// Rule evaluation
public interface IRuleEngine
{
    Task<RuleEvaluationResult> EvaluatePromptAsync(
        string prompt,
        ConversationState state,
        IEnumerable<IPromptRule> rules,
        CancellationToken ct = default);
}

public class RuleEvaluationResult
{
    public IPromptRule? MatchedRule { get; init; }
    public double Confidence { get; init; }
    public RuleResponse? Response { get; init; }
    public TimeSpan EvaluationTime { get; init; }
    public List<RuleMatchAttempt> Attempts { get; init; } // For debugging
}
```

**Example Rules:**

```csharp
// Customer service rule
public class RefundRequestRule : IPromptRule
{
    public string Key => "refund-request";
    public int Priority => 100;

    public double MatchConfidence(PromptContext context)
    {
        var prompt = context.UserPrompt.ToLower();
        var keywords = new[] { "refund", "money back", "return", "cancel order" };
        
        var matches = keywords.Count(k => prompt.Contains(k));
        return Math.Min(matches * 0.4, 1.0);
    }

    public async Task<RuleResponse> GenerateResponseAsync(PromptContext context)
    {
        // Check if we already have order info
        var hasOrderInfo = context.State.Data.ContainsKey("orderId");
        
        if (!hasOrderInfo)
        {
            return new RuleResponse
            {
                Message = "I can help you with your refund request. Could you please provide your order number?",
                UpdateState = new() { ["refundFlow"] = "awaiting_order" },
                NextAction = NextAction.WaitForResponse
            };
        }

        return new RuleResponse
        {
            Message = "I've found your order. Let me check the refund eligibility...",
            InitiateSurveyId = "refund-survey",
            NextAction = NextAction.WaitForResponse
        };
    }
}

// Survey trigger rule
public class FeedbackTriggerRule : IPromptRule
{
    public string Key => "feedback-trigger";
    public int Priority => 50;

    public double MatchConfidence(PromptContext context)
    {
        // Trigger after 5 messages if no survey yet
        var messageCount = context.RecentHistory.Count;
        var hasSurvey = context.State.Data.ContainsKey("surveyCompleted");
        
        return (!hasSurvey && messageCount >= 5) ? 0.7 : 0.0;
    }

    public async Task<RuleResponse> GenerateResponseAsync(PromptContext context)
    {
        return new RuleResponse
        {
            Message = "Quick question - how has your experience been so far?",
            InitiateSurveyId = "quick-feedback",
            RichContent = new RichContent
            {
                Type = "quick-reply",
                Options = new[] { "Great", "Good", "Needs improvement" }
            },
            UpdateState = new() { ["feedbackRequested"] = DateTime.UtcNow },
            NextAction = NextAction.WaitForResponse
        };
    }
}
```

---

## 7) Conversation State Management

**Versioned state for conversation context:**

```csharp
public class ConversationState
{
    public int Version { get; set; } // Optimistic concurrency
    public Guid ConversationId { get; set; }
    public Dictionary<string, object> Data { get; set; } = new();
    public Stack<string> NavigationHistory { get; set; } = new();
    public Dictionary<string, double> Scores { get; set; } = new();
    public HashSet<string> Tags { get; set; } = new();
    public DateTime LastActivityUtc { get; set; }
    public SurveyContext? ActiveSurvey { get; set; }
}

public class SurveyContext
{
    public Guid SurveyId { get; set; }
    public int CurrentQuestionIndex { get; set; }
    public Dictionary<string, object> Answers { get; set; } = new();
    public DateTime StartedAt { get; set; }
}

public interface IConversationStateManager
{
    Task<ConversationState> GetStateAsync(Guid conversationId);
    Task<ConversationState> UpdateStateAsync(ConversationState state);
    Task<bool> TryNavigateBackAsync(Guid conversationId);
}
```

---

## 8) Survey Integration

**Surveys can be triggered by rules or scheduled:**

```csharp
public class Survey
{
    public Guid Id { get; set; }
    public Guid BotId { get; set; }
    public string Name { get; set; }
    public TriggerType TriggerType { get; set; }
    public Dictionary<string, object> TriggerCondition { get; set; } // Rule key, schedule, etc.
    public List<SurveyQuestion> Questions { get; set; }
}

public enum TriggerType
{
    RuleTriggered,    // Triggered by a prompt rule
    Scheduled,        // Time-based (e.g., daily at 3pm)
    Proactive,        // Bot initiates unprompted
    UserRequested     // User explicitly asks for survey
}

public class SurveyQuestion
{
    public string Id { get; set; }
    public string Text { get; set; }
    public QuestionType Type { get; set; }
    public Dictionary<string, object> Options { get; set; }
    public Dictionary<string, object> Validation { get; set; }
    public string? ConditionalLogic { get; set; } // Show based on previous answers
}

public interface ISurveyEngine
{
    Task<SurveyQuestion?> GetNextQuestionAsync(Guid surveyId, Dictionary<string, object> answers);
    Task<bool> ProcessAnswerAsync(Guid conversationId, string questionId, object answer);
    Task<SurveyCompletionResult> CompleteSurveyAsync(Guid conversationId, Guid surveyId);
}
```

---

## 9) Caching Strategy

**Explicit caching for performance:**

```csharp
public interface IBotCache
{
    Task<Bot?> GetBotAsync(Guid botId);
    Task<IReadOnlyList<IPromptRule>> GetRulesAsync(Guid botId);
    Task InvalidateBotAsync(Guid botId);
}

public interface IConversationCache
{
    Task<ConversationState?> GetStateAsync(Guid conversationId);
    Task SetStateAsync(Guid conversationId, ConversationState state, TimeSpan ttl);
}

// Redis implementation with appropriate serialization
public class RedisBotCache : IBotCache
{
    private readonly IConnectionMultiplexer _redis;
    private readonly IDatabase _db;
    
    public async Task<Bot?> GetBotAsync(Guid botId)
    {
        var key = $"bot:{botId}";
        // Get from Redis, fallback to DB, cache on miss
    }
}
```

---

## 10) Multi-tenancy & Security

### Tenant Isolation

**Tenant isolation middleware (even with single schema):**

```csharp
public class TenantContextMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ITenantResolver _tenantResolver;
    private readonly ILogger<TenantContextMiddleware> _logger;

    public async Task InvokeAsync(HttpContext context)
    {
        var tenant = await _tenantResolver.ResolveAsync(context);
        if (tenant == null)
        {
            _logger.LogWarning("Failed to resolve tenant for request {Path}", context.Request.Path);
            context.Response.StatusCode = 401;
            return;
        }
        
        context.Items["Tenant"] = tenant;
        context.Items["TenantId"] = tenant.Id;
        
        // Set up tenant-scoped logging context
        using (_logger.BeginScope(new Dictionary<string, object>
        {
            ["TenantId"] = tenant.Id,
            ["TenantName"] = tenant.Name
        }))
        {
            await _next(context);
        }
    }
}

// EF Core configuration for automatic filtering
public class NexaraDbContext : DbContext
{
    private readonly Guid _tenantId;
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Global query filter for all tenant-scoped entities
        modelBuilder.Entity<Bot>().HasQueryFilter(b => b.TenantId == _tenantId);
        modelBuilder.Entity<Conversation>().HasQueryFilter(c => c.TenantId == _tenantId);
        // ... etc
    }
}
```

### Widget Security

**Embed token structure and validation:**

```csharp
// Embed token payload (JWT)
public class EmbedTokenData
{
    public Guid TenantId { get; set; }
    public Guid BotId { get; set; }
    public List<string> AllowedOrigins { get; set; } // e.g., ["https://customer.com"]
    public DateTime Expiry { get; set; } // Short TTL (1 hour)
    public int RateLimitPerMinute { get; set; } = 100;
}

// Widget initialization with security
[HttpPost("widget/init")]
public async Task<IActionResult> InitializeWidget([FromBody] WidgetInitRequest request)
{
    // Validate embed token
    var tokenData = await _tokenValidator.ValidateAsync(request.Token);
    if (tokenData == null)
        return Unauthorized();
    
    // Check origin
    var origin = Request.Headers["Origin"].ToString();
    if (!tokenData.AllowedOrigins.Contains(origin))
        return Forbid();
    
    // Rate limit check
    if (!await _rateLimiter.AllowRequestAsync(request.Token))
        return StatusCode(429);
    
    // Set CSP headers to prevent unauthorized embedding
    Response.Headers.Add("Content-Security-Policy", 
        $"frame-ancestors {origin} 'self';");
    
    // Generate session token for SignalR
    var sessionToken = GenerateSessionToken(tokenData, origin);
    
    return Ok(new { sessionToken, signalRUrl, botId = tokenData.BotId });
}
```

**Content Security Policy (CSP) implementation:**

```csharp
public class WidgetSecurityMiddleware
{
    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        if (context.Request.Path.StartsWithSegments("/widget"))
        {
            // Decode embed token to get allowed origins
            var token = context.Request.Query["token"];
            var tokenData = await _tokenValidator.ValidateAsync(token);
            
            if (tokenData != null)
            {
                // Set CSP to only allow embedding on authorized domains
                var allowedOrigins = string.Join(" ", tokenData.AllowedOrigins);
                context.Response.Headers.Add("Content-Security-Policy",
                    $"frame-ancestors {allowedOrigins} 'self';");
            }
        }
        await next();
    }
}
```

**SignalR origin validation:**

```csharp
public class ChatHub : Hub
{
    private readonly ITokenValidator _tokenValidator;
    private readonly ILogger<ChatHub> _logger;
    
    public override async Task OnConnectedAsync()
    {
        var httpContext = Context.GetHttpContext();
        var origin = httpContext.Request.Headers["Origin"].ToString();
        var token = httpContext.Request.Query["token"];
        
        // Validate token
        var tokenData = await _tokenValidator.ValidateAsync(token);
        if (tokenData == null)
        {
            Context.Abort();
            return;
        }
        
        // Validate origin matches allowed origins in token
        if (!tokenData.AllowedOrigins.Contains(origin))
        {
            _logger.LogWarning("SignalR connection from unauthorized origin: {Origin}", origin);
            Context.Abort();
            return;
        }
        
        // Store validated context
        Context.Items["TenantId"] = tokenData.TenantId;
        Context.Items["BotId"] = tokenData.BotId;
        Context.Items["Origin"] = origin;
        
        // Add to bot group
        await Groups.AddToGroupAsync(Context.ConnectionId, $"bot-{tokenData.BotId}");
        
        await base.OnConnectedAsync();
    }
}

// CORS configuration for SignalR
builder.Services.AddCors(options =>
{
    options.AddPolicy("WidgetCors", policy =>
    {
        policy.SetIsOriginAllowed(origin => IsOriginAllowedForAnyCustomer(origin))
              .AllowCredentials()
              .AllowAnyHeader()
              .AllowAnyMethod();
    });
});
```

**Rate limiting per embed token:**

```csharp
public class EmbedTokenRateLimiter
{
    private readonly IDistributedCache _cache;
    
    public async Task<bool> AllowRequestAsync(string token, int limitPerMinute = 100)
    {
        var tokenHash = ComputeHash(token); // Don't store raw tokens
        var key = $"rate_limit:embed:{tokenHash}";
        
        var count = await _cache.GetStringAsync(key);
        if (count == null)
        {
            await _cache.SetStringAsync(key, "1", new DistributedCacheEntryOptions
            {
                SlidingExpiration = TimeSpan.FromMinutes(1)
            });
            return true;
        }
        
        var currentCount = int.Parse(count);
        if (currentCount >= limitPerMinute)
        {
            return false;
        }
        
        await _cache.IncrementAsync(key);
        return true;
    }
}

// Apply rate limiting in controllers
[ApiController]
public class ChatApiController : ControllerBase
{
    [HttpPost("widget/message")]
    public async Task<IActionResult> SendMessage(
        [FromHeader(Name = "X-Embed-Token")] string embedToken, 
        [FromBody] MessageRequest request)
    {
        if (!await _rateLimiter.AllowRequestAsync(embedToken))
        {
            return StatusCode(429, "Rate limit exceeded for this widget");
        }
        
        // Process message...
    }
}
```

---

## 11) Observability & Monitoring

**Structured logging with correlation:**

```csharp
public class ObservabilityConfiguration
{
    public static void Configure(WebApplicationBuilder builder)
    {
        // OpenTelemetry
        builder.Services.AddOpenTelemetry()
            .WithTracing(tracing => tracing
                .AddAspNetCoreInstrumentation()
                .AddHttpClientInstrumentation()
                .AddSqlClientInstrumentation()
                .AddSource("Nexara.*"))
            .WithMetrics(metrics => metrics
                .AddAspNetCoreInstrumentation()
                .AddMeter("Nexara.Rules")
                .AddMeter("Nexara.Conversations"));

        // Serilog with structured contexts
        builder.Host.UseSerilog((context, config) =>
        {
            config
                .Enrich.FromLogContext()
                .Enrich.WithProperty("ServiceName", context.HostingEnvironment.ApplicationName)
                .Enrich.WithProperty("Environment", context.HostingEnvironment.EnvironmentName)
                .Enrich.WithCorrelationId()
                .WriteTo.Console(new JsonFormatter())
                .WriteTo.Seq(context.Configuration["Seq:ServerUrl"]);
        });
    }
}

// Custom metrics for rules
public class RuleMetrics
{
    private readonly Meter _meter;
    private readonly Histogram<double> _evaluationTime;
    private readonly Counter<long> _ruleMatches;
    private readonly Histogram<double> _matchConfidence;

    public RuleMetrics()
    {
        _meter = new Meter("Nexara.Rules");
        _evaluationTime = _meter.CreateHistogram<double>("rule.evaluation.time", "ms");
        _ruleMatches = _meter.CreateCounter<long>("rule.matches");
        _matchConfidence = _meter.CreateHistogram<double>("rule.match.confidence");
    }

    public void RecordEvaluation(string ruleKey, double milliseconds, double confidence)
    {
        _evaluationTime.Record(milliseconds, new("rule", ruleKey));
        if (confidence > 0.5)
        {
            _ruleMatches.Add(1, new("rule", ruleKey));
            _matchConfidence.Record(confidence, new("rule", ruleKey));
        }
    }
}
```

---

## 12) SignalR Configuration

**Redis backplane for horizontal scaling (even in Railway):**

```csharp
// In Program.cs
var redisConnection = builder.Configuration.GetConnectionString("Redis");

builder.Services.AddSignalR(options =>
{
    options.EnableDetailedErrors = builder.Environment.IsDevelopment();
    options.KeepAliveInterval = TimeSpan.FromSeconds(15);
})
.AddStackExchangeRedis(redisConnection, options => 
{
    options.Configuration.ChannelPrefix = $"nexara_{builder.Environment.EnvironmentName}";
});

// Message delivery acknowledgments
public class ChatHub : Hub
{
    public async Task SendMessage(string conversationId, ChatMessage message)
    {
        message.Status = MessageStatus.Sent;
        await _messageService.SaveMessageAsync(message);
        
        await Clients.Group(conversationId).SendAsync("ReceiveMessage", message);
    }
    
    public async Task AckMessage(string messageId)
    {
        await _messageService.UpdateStatusAsync(messageId, MessageStatus.Delivered);
    }
    
    public async Task MarkMessageRead(string messageId)
    {
        await _messageService.UpdateStatusAsync(messageId, MessageStatus.Read);
    }
}
```

---

## 13) Testing Strategy

**Comprehensive testing approach:**

```csharp
// Unit tests for rules
[TestClass]
public class RefundRequestRuleTests
{
    [TestMethod]
    public void Should_Match_Refund_Keywords()
    {
        var rule = new RefundRequestRule();
        var context = new PromptContext
        {
            UserPrompt = "I want a refund for my order",
            ConversationId = Guid.NewGuid(),
            State = new ConversationState(),
            UserAttributes = new(),
            RecentHistory = new(),
            BotConfig = new Bot()
        };
        
        var confidence = rule.MatchConfidence(context);
        Assert.IsTrue(confidence > 0.5);
    }
}

// Property-based testing for rules
[TestClass]
public class RulePropertyTests
{
    [TestMethod]
    public void Rules_Should_Be_Deterministic()
    {
        Prop.ForAll<PromptContext>(context =>
        {
            var rule = new RefundRequestRule();
            var confidence1 = rule.MatchConfidence(context);
            var confidence2 = rule.MatchConfidence(context);
            return confidence1 == confidence2;
        }).QuickCheckThrowOnFailure();
    }
}

// Integration tests
[TestClass]
public class ConversationIntegrationTests
{
    [TestMethod]
    public async Task Complete_Conversation_With_Survey()
    {
        // Test full conversation flow with survey trigger
        var client = _factory.CreateClient();
        var conversationId = await StartConversation(client);
        
        await SendMessage(client, conversationId, "I need help with a refund");
        var response = await GetResponse(client, conversationId);
        
        Assert.IsTrue(response.Message.Contains("order number"));
        
        await SendMessage(client, conversationId, "12345");
        response = await GetResponse(client, conversationId);
        
        Assert.IsNotNull(response.SurveyId);
    }
}

// Load testing for SignalR
public class SignalRLoadTest
{
    [Test]
    public async Task Handle_1000_Concurrent_Conversations()
    {
        var connections = new List<HubConnection>();
        // Create and connect 1000 clients
        // Assert message delivery under load
    }
}
```

---

## 14) Development Environment – .NET Aspire

**Why Aspire?** One-command orchestration for all services + infra with a rich dashboard (logs/traces/health).

**AppHost composition (example):**
- Infra: Postgres (`WithDataVolume()`), Redis, Seq (logs), OTLP collector (traces)
- Services: `Nexara.Api`, `Nexara.Realtime`, `Nexara.Worker`
- Optionally: run Admin (Vite) as an executable for local preview

**Daily workflow:**
```bash
dotnet run -p src/Nexara.AppHost
# Aspire dashboard URL is printed to console
```

---

## 15) Hosting & Deployment

### MVP (Railway-first)
- **Platform:** Railway for **Nexara.Api**, **Nexara.Realtime**, **Nexara.Worker** (as separate services).
- **Data:** Railway Postgres & Redis (or Neon Postgres + Railway/Upstash Redis).
- **Domains:** `api.nexara.com`, `realtime.nexara.com` pointing to Railway services.
- **Frontends:** Vercel for Admin & widget assets (global CDN, preview deploys).
- **CI/CD:** GitHub Actions → build & push Docker images → Railway deploy; Vercel deploy for frontends.

### Scaling beyond Railway
- **More throughput (single region):** Raise Railway plan, tune process counts, add Redis & DB connection pools; split Worker to its own plan.
- **Global low-latency realtime:** Move API/Realtime to **Fly.io** in 2–3 regions (e.g., iad/lhr/syd) with Redis backplane near hubs; keep Postgres serverless (Neon) or managed with read replicas.
- **Enterprise & Azure alignment:** Migrate services to **Azure Container Apps** and use **Azure SignalR Service**; keep Postgres on Azure Database for PostgreSQL and Redis on Azure Cache.
- **LLM Integration:** Add Azure OpenAI or OpenAI API for fallback responses when no rules match.
- **Data warehouse:** Add BigQuery/Snowflake sync via periodic Worker jobs.
- **High availability:** Introduce health-check based rollouts, blue/green, and cross-region failover (DNS + active/active).

---

## 16) Dev → Prod Flow
1. **Contracts:** OpenAPI → NSwag generates C# and TypeScript clients.
2. **Local dev:** Aspire runs full stack; seed DB with demo bots and rules.
3. **Tests:** Unit (rules/chat engine), integration (API/DB), E2E (Playwright: complete conversation), property-based (FsCheck).
4. **CI/CD:** Actions build, test, publish images; deploy to Railway (per service).
5. **Monitoring:** OTEL traces + Serilog → Seq; custom metrics for rule evaluation; alerts from APM/log platform.

---

## 17) Milestones

**Week 1**
- EF Core schema + RLS; seed data & migrations
- OpenAPI contracts; NSwag C# & TS clients
- Aspire AppHost wiring (Postgres, Redis, Seq, OTEL)
- TenantContextMiddleware + EF Core global filters

**Week 2**
- C# prompt rules interfaces + IRuleEngine + initial rule set
- ConversationState management with versioning
- ChatEngine core logic + audit trail via RuleEvaluationResult
- API endpoints (bots, conversations, messages) + happy-path E2E

**Week 3**
- SignalR realtime service with Redis backplane; reconnect & acks
- Message delivery tracking (Sent/Delivered/Read)
- Survey engine integration (triggered and scheduled)
- Widget v1 (TS Web Component); Admin skeleton (React, rule editor)

**Week 4**
- Worker jobs (scheduled surveys, idle timeouts, webhook retries)
- Bot configuration UI; rule testing console
- Caching layer (IBotCache, IConversationCache)
- Basic analytics with custom metrics; Railway deployment

**Week 5**
- Property-based tests for rules; load tests for SignalR
- Slack/Teams connector integration
- Enhanced rule capabilities (context awareness, multi-turn)
- **MVP Ready** – configure bot, define rules, embed widget, handle conversations

**Weeks 6-7**
- LLM fallback integration for unmatched prompts
- CRM connectors, webhook delivery system
- Advanced analytics dashboards (conversation flow, rule effectiveness)
- PII encryption, data retention, feature flags
- v1.0 launch & scale plan refinement

---

## 18) Acceptance Criteria (MVP)
- Given prompt "I want a refund", the **RefundRequestRule** matches with >0.8 confidence and responds appropriately.
- Bot can maintain conversation context across multiple messages and reference previous answers.
- Survey can be triggered mid-conversation based on rules or scheduled for proactive outreach.
- Widget connects via SignalR, receives bot response <300ms p95 (local/dev).
- Message delivery tracking shows Sent → Delivered → Read lifecycle.
- Rules execute deterministically with same input producing same output.
- `conversation.completed` webhook is delivered with signature; retry on 5xx.
- Admin can configure bot personality and test conversations in preview mode.
- TenantContextMiddleware validates all requests; EF Core filters apply automatically.
- Rule evaluation produces audit trail with confidence scores and timing metrics.
- Widget security enforces:
  - CSP headers prevent embedding on unauthorized domains
  - SignalR connections reject invalid origins
  - Rate limiting prevents abuse (100 requests/minute per embed token)
- Bot gracefully handles unmatched prompts with configurable fallback responses.