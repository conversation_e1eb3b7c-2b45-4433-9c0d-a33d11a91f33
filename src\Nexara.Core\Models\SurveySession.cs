namespace Nexara.Core.Models;

public enum SurveySessionStatus
{
    Active,
    Completed,
    Abandoned
}

public class SurveySession
{
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public Guid ConversationId { get; set; }
    
    public Guid SurveyId { get; set; }
    
    public SurveySessionStatus Status { get; set; } = SurveySessionStatus.Active;
    
    public DateTime StartedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? CompletedAt { get; set; }
    
    public string AnswersJson { get; set; } = "{}";
    
    // Navigation properties
    public virtual Conversation Conversation { get; set; } = null!;
    public virtual Survey Survey { get; set; } = null!;
}