// Shared types matching the .NET API contracts

export interface Bot {
  id: string;
  tenantId: string;
  name: string;
  description?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Conversation {
  id: string;
  botId: string;
  sessionId: string;
  userId?: string;
  startedAt: Date;
  endedAt?: Date;
  isActive: boolean;
}

export interface Message {
  id: string;
  conversationId: string;
  content: string;
  isFromUser: boolean;
  timestamp: Date;
  messageType: string;
}

export interface Survey {
  id: string;
  botId: string;
  title: string;
  description?: string;
  isActive: boolean;
  createdAt: Date;
  nodes: SurveyNode[];
}

export interface SurveyNode {
  id: string;
  surveyId: string;
  nodeType: string;
  title: string;
  content?: string;
  options?: string[];
  isRequired: boolean;
  orderIndex: number;
}

export interface ConversationState {
  id: string;
  conversationId: string;
  data: Record<string, any>;
  version: number;
  lastModified: Date;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  errors?: Record<string, string[]>;
}

export interface PaginatedResponse<T> {
  items: T[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
}

export interface CreateBotRequest {
  name: string;
  description?: string;
}

export interface UpdateBotRequest {
  name?: string;
  description?: string;
  isActive?: boolean;
}

export interface SendMessageRequest {
  conversationId: string;
  content: string;
}

export interface CreateConversationRequest {
  botId: string;
  userId?: string;
}

export interface MessageResponse {
  id: string;
  conversationId: string;
  content: string;
  isFromUser: boolean;
  timestamp: Date;
  messageType: string;
  ruleMatched?: string;
  confidence?: number;
}

export interface ConversationResponse {
  id: string;
  botId: string;
  sessionId: string;
  userId?: string;
  startedAt: Date;
  endedAt?: Date;
  isActive: boolean;
  messages: MessageResponse[];
}

export interface ChatConnectionInfo {
  hubUrl: string;
  accessToken: string;
}