using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Nexara.Core.Data;
using Nexara.Core.Models;

namespace Nexara.Core.MultiTenancy;

public class TenantResolver : ITenantResolver
{
    private readonly NexaraDbContext _context;
    private readonly ILogger<TenantResolver> _logger;

    public TenantResolver(NexaraDbContext context, ILogger<TenantResolver> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<Tenant?> ResolveAsync(HttpContext context)
    {
        try
        {
            // Try to resolve from JWT claims first
            var tenantIdClaim = context.User.FindFirst("tenant_id")?.Value;
            if (Guid.TryParse(tenantIdClaim, out var tenantId))
            {
                return await ResolveByIdAsync(tenantId);
            }

            // Try to resolve from custom header
            if (context.Request.Headers.TryGetValue("X-Tenant-Id", out var tenantIdHeader))
            {
                if (Guid.TryParse(tenantIdHeader.FirstOrDefault(), out var headerTenantId))
                {
                    return await ResolveByIdAsync(headerTenantId);
                }
            }

            // Try to resolve from custom header by name
            if (context.Request.Headers.TryGetValue("X-Tenant-Name", out var tenantNameHeader))
            {
                var tenantName = tenantNameHeader.FirstOrDefault();
                if (!string.IsNullOrEmpty(tenantName))
                {
                    return await ResolveByNameAsync(tenantName);
                }
            }

            // Try to resolve from subdomain
            var host = context.Request.Host.Host;
            if (host.Contains('.'))
            {
                var subdomain = host.Split('.')[0];
                if (!string.IsNullOrEmpty(subdomain) && subdomain != "www" && subdomain != "api")
                {
                    return await ResolveByNameAsync(subdomain);
                }
            }

            _logger.LogWarning("Unable to resolve tenant from request");
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving tenant from request");
            return null;
        }
    }

    public async Task<Tenant?> ResolveByIdAsync(Guid tenantId)
    {
        try
        {
            return await _context.Tenants
                .AsNoTracking()
                .FirstOrDefaultAsync(t => t.Id == tenantId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving tenant by ID {TenantId}", tenantId);
            return null;
        }
    }

    public async Task<Tenant?> ResolveByNameAsync(string tenantName)
    {
        try
        {
            return await _context.Tenants
                .AsNoTracking()
                .FirstOrDefaultAsync(t => t.Name.ToLower() == tenantName.ToLower());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving tenant by name {TenantName}", tenantName);
            return null;
        }
    }
}