using Microsoft.Extensions.Caching.Distributed;
using Nexara.Core.Models;
using System.Text.Json;

namespace Nexara.Core.Services;

public class ConversationCache : IConversationCache
{
    private readonly IDistributedCache _cache;

    public ConversationCache(IDistributedCache cache)
    {
        _cache = cache;
    }

    public async Task<ConversationState?> GetStateAsync(Guid conversationId)
    {
        var cacheKey = $"conversation_state:{conversationId}";
        var cachedState = await _cache.GetStringAsync(cacheKey);

        if (string.IsNullOrEmpty(cachedState))
        {
            return null;
        }

        return JsonSerializer.Deserialize<ConversationState>(cachedState);
    }

    public async Task SetStateAsync(Guid conversationId, ConversationState state, TimeSpan ttl)
    {
        var cacheKey = $"conversation_state:{conversationId}";
        var serializedState = JsonSerializer.Serialize(state);

        await _cache.SetStringAsync(cacheKey, serializedState,
            new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = ttl
            });
    }

    public async Task InvalidateStateAsync(Guid conversationId)
    {
        var cacheKey = $"conversation_state:{conversationId}";
        await _cache.RemoveAsync(cacheKey);
    }
}