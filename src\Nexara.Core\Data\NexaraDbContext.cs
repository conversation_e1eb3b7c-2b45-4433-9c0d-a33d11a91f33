using Microsoft.EntityFrameworkCore;
using Nexara.Core.Models;

namespace Nexara.Core.Data;

public class NexaraDbContext : DbContext
{
    private readonly ITenantProvider? _tenantProvider;

    public NexaraDbContext(DbContextOptions<NexaraDbContext> options, ITenantProvider? tenantProvider = null) 
        : base(options)
    {
        _tenantProvider = tenantProvider;
    }

    public DbSet<Tenant> Tenants { get; set; }
    public DbSet<User> Users { get; set; }
    public DbSet<Bot> Bots { get; set; }
    public DbSet<RuleSet> RuleSets { get; set; }
    public DbSet<PromptRule> PromptRules { get; set; }
    public DbSet<Survey> Surveys { get; set; }
    public DbSet<Conversation> Conversations { get; set; }
    public DbSet<ConversationState> ConversationStates { get; set; }
    public DbSet<Message> Messages { get; set; }
    public DbSet<UserResponse> UserResponses { get; set; }
    public DbSet<SurveySession> SurveySessions { get; set; }
    public DbSet<Event> Events { get; set; }
    
    // Keep old entities for backward compatibility during migration
    public DbSet<SurveyNode> SurveyNodes { get; set; }
    public DbSet<SurveyEdge> SurveyEdges { get; set; }
    public DbSet<RuleDefinition> RuleDefinitions { get; set; }
    public DbSet<Session> Sessions { get; set; }
    public DbSet<SessionState> SessionStates { get; set; }
    public DbSet<Answer> Answers { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        
        ConfigureEntities(modelBuilder);
        ConfigureMultiTenancy(modelBuilder);
        ConfigureIndexes(modelBuilder);
    }

    private void ConfigureEntities(ModelBuilder modelBuilder)
    {
        // Tenant
        modelBuilder.Entity<Tenant>(entity =>
        {
            entity.HasIndex(e => e.Name).IsUnique();
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
        });

        // User
        modelBuilder.Entity<User>(entity =>
        {
            entity.HasIndex(e => new { e.TenantId, e.Email }).IsUnique();
            entity.HasOne(e => e.Tenant).WithMany(e => e.Users).HasForeignKey(e => e.TenantId);
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
        });

        // Bot
        modelBuilder.Entity<Bot>(entity =>
        {
            entity.HasIndex(e => new { e.TenantId, e.Name }).IsUnique();
            entity.HasOne(e => e.Tenant).WithMany().HasForeignKey(e => e.TenantId);
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
        });

        // RuleSet
        modelBuilder.Entity<RuleSet>(entity =>
        {
            entity.HasIndex(e => new { e.BotId, e.Name }).IsUnique();
            entity.HasOne(e => e.Bot).WithMany(e => e.RuleSets).HasForeignKey(e => e.BotId);
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
        });

        // PromptRule
        modelBuilder.Entity<PromptRule>(entity =>
        {
            entity.HasIndex(e => new { e.RuleSetId, e.Key }).IsUnique();
            entity.HasOne(e => e.RuleSet).WithMany(e => e.PromptRules).HasForeignKey(e => e.RuleSetId);
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
        });

        // Survey (updated for bot relationship)
        modelBuilder.Entity<Survey>(entity =>
        {
            entity.HasIndex(e => new { e.BotId, e.Name }).IsUnique();
            entity.HasOne(e => e.Bot).WithMany(e => e.Surveys).HasForeignKey(e => e.BotId);
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
        });

        // Conversation
        modelBuilder.Entity<Conversation>(entity =>
        {
            entity.HasIndex(e => new { e.TenantId, e.UserExternalId });
            entity.HasIndex(e => e.LastActivityAt);
            entity.HasOne(e => e.Tenant).WithMany().HasForeignKey(e => e.TenantId);
            entity.HasOne(e => e.Bot).WithMany(e => e.Conversations).HasForeignKey(e => e.BotId);
        });

        // ConversationState
        modelBuilder.Entity<ConversationState>(entity =>
        {
            entity.HasIndex(e => new { e.ConversationId, e.Version }).IsUnique();
            entity.HasOne(e => e.Conversation).WithMany(e => e.ConversationStates).HasForeignKey(e => e.ConversationId);
        });

        // Message (updated for conversation)
        modelBuilder.Entity<Message>(entity =>
        {
            entity.HasIndex(e => new { e.ConversationId, e.CreatedAt });
            entity.HasOne(e => e.Conversation).WithMany(e => e.Messages).HasForeignKey(e => e.ConversationId);
        });

        // UserResponse
        modelBuilder.Entity<UserResponse>(entity =>
        {
            entity.HasIndex(e => new { e.ConversationId, e.Timestamp });
            entity.HasOne(e => e.Conversation).WithMany(e => e.UserResponses).HasForeignKey(e => e.ConversationId);
            entity.HasOne(e => e.Message).WithMany(e => e.UserResponses).HasForeignKey(e => e.MessageId);
        });

        // SurveySession
        modelBuilder.Entity<SurveySession>(entity =>
        {
            entity.HasIndex(e => new { e.ConversationId, e.SurveyId });
            entity.HasOne(e => e.Conversation).WithMany(e => e.SurveySessions).HasForeignKey(e => e.ConversationId);
            entity.HasOne(e => e.Survey).WithMany(e => e.SurveySessions).HasForeignKey(e => e.SurveyId);
        });

        // Event (updated for conversation)
        modelBuilder.Entity<Event>(entity =>
        {
            entity.HasIndex(e => new { e.ConversationId, e.Timestamp });
            entity.HasIndex(e => new { e.Type, e.Timestamp });
            entity.HasOne(e => e.Conversation).WithMany(e => e.Events).HasForeignKey(e => e.ConversationId);
        });

    }


    private void ConfigureMultiTenancy(ModelBuilder modelBuilder)
    {
        // Apply global query filters for tenant isolation
        var tenantId = _tenantProvider?.GetTenantId();
        
        if (tenantId.HasValue)
        {
            // New entities
            modelBuilder.Entity<User>().HasQueryFilter(e => e.TenantId == tenantId.Value);
            modelBuilder.Entity<Bot>().HasQueryFilter(e => e.TenantId == tenantId.Value);
            modelBuilder.Entity<Conversation>().HasQueryFilter(e => e.TenantId == tenantId.Value);
            
        }
    }

    private void ConfigureIndexes(ModelBuilder modelBuilder)
    {
        // Additional performance indexes for new entities
        modelBuilder.Entity<Bot>().HasIndex(e => e.Status);
        modelBuilder.Entity<Survey>().HasIndex(e => e.Status);
        modelBuilder.Entity<Conversation>().HasIndex(e => e.Status);
        modelBuilder.Entity<Message>().HasIndex(e => e.Status);
        modelBuilder.Entity<PromptRule>().HasIndex(e => e.Priority);
        
    }
}