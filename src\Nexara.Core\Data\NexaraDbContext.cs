using Microsoft.EntityFrameworkCore;
using Nexara.Core.Models;

namespace Nexara.Core.Data;

public class NexaraDbContext : DbContext
{
    private readonly ITenantProvider? _tenantProvider;

    public NexaraDbContext(DbContextOptions<NexaraDbContext> options, ITenantProvider? tenantProvider = null) 
        : base(options)
    {
        _tenantProvider = tenantProvider;
    }

    public DbSet<Tenant> Tenants { get; set; }
    public DbSet<User> Users { get; set; }
    public DbSet<Survey> Surveys { get; set; }
    public DbSet<SurveyNode> SurveyNodes { get; set; }
    public DbSet<SurveyEdge> SurveyEdges { get; set; }
    public DbSet<RuleDefinition> RuleDefinitions { get; set; }
    public DbSet<Session> Sessions { get; set; }
    public DbSet<SessionState> SessionStates { get; set; }
    public DbSet<Message> Messages { get; set; }
    public DbSet<Answer> Answers { get; set; }
    public DbSet<Event> Events { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        
        ConfigureEntities(modelBuilder);
        ConfigureMultiTenancy(modelBuilder);
        ConfigureIndexes(modelBuilder);
    }

    private void ConfigureEntities(ModelBuilder modelBuilder)
    {
        // Tenant
        modelBuilder.Entity<Tenant>(entity =>
        {
            entity.HasIndex(e => e.Name).IsUnique();
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
        });

        // User
        modelBuilder.Entity<User>(entity =>
        {
            entity.HasIndex(e => new { e.TenantId, e.Email }).IsUnique();
            entity.HasOne(e => e.Tenant).WithMany(e => e.Users).HasForeignKey(e => e.TenantId);
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
        });

        // Survey
        modelBuilder.Entity<Survey>(entity =>
        {
            entity.HasIndex(e => new { e.TenantId, e.Name }).IsUnique();
            entity.HasOne(e => e.Tenant).WithMany(e => e.Surveys).HasForeignKey(e => e.TenantId);
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
        });

        // SurveyNode
        modelBuilder.Entity<SurveyNode>(entity =>
        {
            entity.HasIndex(e => new { e.SurveyId, e.NodeKey }).IsUnique();
            entity.HasOne(e => e.Survey).WithMany(e => e.Nodes).HasForeignKey(e => e.SurveyId);
        });

        // SurveyEdge
        modelBuilder.Entity<SurveyEdge>(entity =>
        {
            entity.HasOne(e => e.Survey).WithMany(e => e.Edges).HasForeignKey(e => e.SurveyId);
            entity.HasOne(e => e.FromNode).WithMany(e => e.OutgoingEdges).HasForeignKey(e => e.FromNodeId).OnDelete(DeleteBehavior.Restrict);
            entity.HasOne(e => e.ToNode).WithMany(e => e.IncomingEdges).HasForeignKey(e => e.ToNodeId).OnDelete(DeleteBehavior.Restrict);
        });

        // RuleDefinition
        modelBuilder.Entity<RuleDefinition>(entity =>
        {
            entity.HasIndex(e => new { e.SurveyId, e.RuleKey }).IsUnique();
            entity.HasOne(e => e.Survey).WithMany(e => e.RuleDefinitions).HasForeignKey(e => e.SurveyId);
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
        });

        // Session
        modelBuilder.Entity<Session>(entity =>
        {
            entity.HasIndex(e => new { e.TenantId, e.UserExternalId });
            entity.HasIndex(e => e.LastActivityAt);
            entity.HasOne(e => e.Tenant).WithMany(e => e.Sessions).HasForeignKey(e => e.TenantId);
            entity.HasOne(e => e.Survey).WithMany(e => e.Sessions).HasForeignKey(e => e.SurveyId);
        });

        // SessionState
        modelBuilder.Entity<SessionState>(entity =>
        {
            entity.HasIndex(e => new { e.SessionId, e.Version }).IsUnique();
            entity.HasOne(e => e.Session).WithMany(e => e.SessionStates).HasForeignKey(e => e.SessionId);
        });

        // Message
        modelBuilder.Entity<Message>(entity =>
        {
            entity.HasIndex(e => new { e.SessionId, e.CreatedAt });
            entity.HasOne(e => e.Session).WithMany(e => e.Messages).HasForeignKey(e => e.SessionId);
        });

        // Answer
        modelBuilder.Entity<Answer>(entity =>
        {
            entity.HasIndex(e => new { e.SessionId, e.NodeId });
            entity.HasOne(e => e.Session).WithMany(e => e.Answers).HasForeignKey(e => e.SessionId);
            entity.HasOne(e => e.Node).WithMany(e => e.Answers).HasForeignKey(e => e.NodeId);
        });

        // Event
        modelBuilder.Entity<Event>(entity =>
        {
            entity.HasIndex(e => new { e.SessionId, e.Timestamp });
            entity.HasIndex(e => new { e.Type, e.Timestamp });
            entity.HasOne(e => e.Session).WithMany(e => e.Events).HasForeignKey(e => e.SessionId);
        });
    }

    private void ConfigureMultiTenancy(ModelBuilder modelBuilder)
    {
        // Apply global query filters for tenant isolation
        var tenantId = _tenantProvider?.GetTenantId();
        
        if (tenantId.HasValue)
        {
            modelBuilder.Entity<User>().HasQueryFilter(e => e.TenantId == tenantId.Value);
            modelBuilder.Entity<Survey>().HasQueryFilter(e => e.TenantId == tenantId.Value);
            modelBuilder.Entity<Session>().HasQueryFilter(e => e.TenantId == tenantId.Value);
        }
    }

    private void ConfigureIndexes(ModelBuilder modelBuilder)
    {
        // Additional performance indexes
        modelBuilder.Entity<Survey>().HasIndex(e => e.Status);
        modelBuilder.Entity<Session>().HasIndex(e => e.Status);
        modelBuilder.Entity<Message>().HasIndex(e => e.Status);
        modelBuilder.Entity<SurveyNode>().HasIndex(e => e.Type);
    }
}