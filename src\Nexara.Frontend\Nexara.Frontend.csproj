<Project Sdk="Microsoft.Build.NoTargets/3.7.56">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <IsPackable>false</IsPackable>
    <SpaRoot>.</SpaRoot>
    <DefaultItemExcludes>$(DefaultItemExcludes);node_modules\**</DefaultItemExcludes>
  </PropertyGroup>

  <ItemGroup>
    <None Include="**" Exclude="$(DefaultItemExcludes)" />
  </ItemGroup>

  <Target Name="Build" DependsOnTargets="InstallNodeModules;BuildSpa" />
  <Target Name="Publish" DependsOnTargets="Build;PublishSpa" />

  <Target Name="InstallNodeModules" Condition="!Exists('node_modules')">
    <Message Importance="high" Text="Installing Node.js dependencies..." />
    <Exec Command="npm install" WorkingDirectory="$(SpaRoot)" />
  </Target>

  <Target Name="BuildSpa" DependsOnTargets="InstallNodeModules">
    <Message Importance="high" Text="Building React application..." />
    <Exec Command="npm run build" WorkingDirectory="$(SpaRoot)" />
  </Target>

  <Target Name="PublishSpa" DependsOnTargets="BuildSpa">
    <Message Importance="high" Text="Publishing React application..." />
    <ItemGroup>
      <DistFiles Include="$(SpaRoot)dist\**" />
    </ItemGroup>
    <Copy SourceFiles="@(DistFiles)" DestinationFolder="$(PublishDir)wwwroot\%(RecursiveDir)" />
  </Target>

</Project>