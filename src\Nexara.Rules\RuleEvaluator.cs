using System.Diagnostics;
using Microsoft.Extensions.Logging;

namespace Nexara.Rules;

public class RuleEvaluator : IRuleEvaluator
{
    private readonly ILogger<RuleEvaluator> _logger;

    public RuleEvaluator(ILogger<RuleEvaluator> logger)
    {
        _logger = logger;
    }

    public async Task<RuleEvaluationResult> EvaluateAsync(
        IEnumerable<INexaraRule> rules,
        RuleContext context,
        CancellationToken ct = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var matchedRules = new List<RuleMatch>();
        string? finalNodeId = null;

        try
        {
            // Order rules by priority (descending) then by registration order
            var orderedRules = rules.OrderByDescending(r => r.Priority).ToList();

            _logger.LogDebug("Evaluating {RuleCount} rules for session {SessionId}", 
                orderedRules.Count, context.SessionId);

            foreach (var rule in orderedRules)
            {
                ct.ThrowIfCancellationRequested();

                try
                {
                    var applies = rule.Applies(context);
                    
                    if (applies)
                    {
                        var action = rule.Evaluate(context);
                        
                        var match = new RuleMatch
                        {
                            RuleKey = rule.Key,
                            Priority = rule.Priority,
                            Action = action,
                            Applied = true
                        };

                        matchedRules.Add(match);

                        // Apply score deltas
                        if (action.ScoreDelta != null)
                        {
                            foreach (var (key, delta) in action.ScoreDelta)
                            {
                                if (context.Scores.ContainsKey(key))
                                {
                                    context.Scores[key] += delta;
                                }
                                else
                                {
                                    context.Scores[key] = delta;
                                }
                            }
                        }

                        // Add tags
                        if (action.AddTags != null)
                        {
                            foreach (var tag in action.AddTags)
                            {
                                context.Tags.Add(tag);
                            }
                        }

                        // Set final node (latest rule with GotoNodeId wins)
                        if (!string.IsNullOrEmpty(action.GotoNodeId))
                        {
                            finalNodeId = action.GotoNodeId;
                        }

                        // For first-match policy, break after first rule fires
                        // This can be configurable in the future
                        break;
                    }
                    else
                    {
                        var match = new RuleMatch
                        {
                            RuleKey = rule.Key,
                            Priority = rule.Priority,
                            Action = new RuleAction(),
                            Applied = false
                        };

                        matchedRules.Add(match);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error evaluating rule {RuleKey} for session {SessionId}", 
                        rule.Key, context.SessionId);
                    
                    // Continue with other rules even if one fails
                    var match = new RuleMatch
                    {
                        RuleKey = rule.Key,
                        Priority = rule.Priority,
                        Action = new RuleAction(),
                        Applied = false
                    };

                    matchedRules.Add(match);
                }
            }

            return new RuleEvaluationResult
            {
                NextNodeId = finalNodeId,
                MatchedRules = matchedRules,
                EvaluationTime = stopwatch.Elapsed,
                EvaluatedAt = DateTime.UtcNow
            };
        }
        finally
        {
            stopwatch.Stop();
            _logger.LogDebug("Rule evaluation completed in {ElapsedMs}ms for session {SessionId}, {AppliedRules} rules applied",
                stopwatch.ElapsedMilliseconds, context.SessionId, matchedRules.Count(r => r.Applied));
        }
    }
}