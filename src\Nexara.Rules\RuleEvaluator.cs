using System.Diagnostics;
using Microsoft.Extensions.Logging;

namespace Nexara.Rules;

public class RuleEngine : IRuleEngine
{
    private readonly ILogger<RuleEngine> _logger;

    public RuleEngine(ILogger<RuleEngine> logger)
    {
        _logger = logger;
    }

    public async Task<RuleEvaluationResult> EvaluatePromptAsync(
        string prompt,
        ConversationStateData state,
        IEnumerable<IPromptRule> rules,
        CancellationToken ct = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var attempts = new List<RuleMatchAttempt>();
        
        var context = new PromptContext
        {
            UserPrompt = prompt,
            ConversationId = state.ConversationId,
            State = state,
            UserAttributes = new Dictionary<string, object>(),
            RecentHistory = new List<MessageData>(),
            BotConfig = new BotConfigData()
        };

        try
        {
            // Order rules by priority (descending)
            var orderedRules = rules.OrderByDescending(r => r.Priority).ToList();

            _logger.LogDebug("Evaluating {RuleCount} prompt rules for conversation {ConversationId}", 
                orderedRules.Count, state.ConversationId);

            IPromptRule? bestMatch = null;
            double bestConfidence = 0.0;
            RuleResponse? bestResponse = null;

            foreach (var rule in orderedRules)
            {
                ct.ThrowIfCancellationRequested();

                var ruleStopwatch = Stopwatch.StartNew();
                
                try
                {
                    var confidence = rule.MatchConfidence(context);
                    
                    var attempt = new RuleMatchAttempt
                    {
                        RuleKey = rule.Key,
                        Priority = rule.Priority,
                        Confidence = confidence,
                        Matched = confidence > 0.0,
                        EvaluationTime = ruleStopwatch.Elapsed
                    };
                    
                    attempts.Add(attempt);
                    
                    if (confidence > bestConfidence)
                    {
                        bestConfidence = confidence;
                        bestMatch = rule;
                        bestResponse = await rule.GenerateResponseAsync(context);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error evaluating prompt rule {RuleKey} for conversation {ConversationId}", 
                        rule.Key, state.ConversationId);
                    
                    var attempt = new RuleMatchAttempt
                    {
                        RuleKey = rule.Key,
                        Priority = rule.Priority,
                        Confidence = 0.0,
                        Matched = false,
                        EvaluationTime = ruleStopwatch.Elapsed
                    };
                    
                    attempts.Add(attempt);
                }
                finally
                {
                    ruleStopwatch.Stop();
                }
            }

            return new RuleEvaluationResult
            {
                MatchedRule = bestMatch,
                Confidence = bestConfidence,
                Response = bestResponse,
                EvaluationTime = stopwatch.Elapsed,
                Attempts = attempts
            };
        }
        finally
        {
            stopwatch.Stop();
            _logger.LogDebug("Prompt rule evaluation completed in {ElapsedMs}ms for conversation {ConversationId}, best confidence: {Confidence}",
                stopwatch.ElapsedMilliseconds, state.ConversationId, attempts.Where(a => a.Matched).Max(a => (double?)a.Confidence) ?? 0.0);
        }
    }
}