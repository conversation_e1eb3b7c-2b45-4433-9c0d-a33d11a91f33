namespace Nexara.Rules;

public class RuleMatchAttempt
{
    public required string RuleKey { get; init; }
    public int Priority { get; init; }
    public double Confidence { get; init; }
    public bool Matched { get; init; }
    public TimeSpan EvaluationTime { get; init; }
}

public class RuleEvaluationResult
{
    public IPromptRule? MatchedRule { get; init; }
    public double Confidence { get; init; }
    public RuleResponse? Response { get; init; }
    public TimeSpan EvaluationTime { get; init; }
    public List<RuleMatchAttempt> Attempts { get; init; } = new();
}