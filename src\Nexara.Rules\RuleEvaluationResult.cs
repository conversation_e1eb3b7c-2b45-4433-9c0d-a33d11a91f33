namespace Nexara.Rules;

public class RuleEvaluationResult
{
    public string? NextNodeId { get; init; }
    public IReadOnlyList<RuleMatch> MatchedRules { get; init; } = Array.Empty<RuleMatch>();
    public TimeSpan EvaluationTime { get; init; }
    public DateTime EvaluatedAt { get; init; } = DateTime.UtcNow;
}

public class RuleMatch
{
    public required string RuleKey { get; init; }
    public required int Priority { get; init; }
    public required RuleAction Action { get; init; }
    public required bool Applied { get; init; }
}