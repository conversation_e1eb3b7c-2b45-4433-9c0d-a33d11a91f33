using System.Diagnostics.Metrics;

namespace Nexara.Core.Observability;

public class RuleMetrics
{
    private readonly Meter _meter;
    private readonly Histogram<double> _evaluationTime;
    private readonly Counter<long> _ruleMatches;
    private readonly Counter<long> _ruleEvaluations;

    public RuleMetrics()
    {
        _meter = new Meter("Nexara.Rules", "1.0.0");
        _evaluationTime = _meter.CreateHistogram<double>("rule.evaluation.duration", "ms", "Duration of rule evaluation");
        _ruleMatches = _meter.CreateCounter<long>("rule.matches.total", description: "Total number of rule matches");
        _ruleEvaluations = _meter.CreateCounter<long>("rule.evaluations.total", description: "Total number of rule evaluations");
    }

    public void RecordEvaluation(string ruleKey, double milliseconds, bool matched, Guid sessionId, Guid tenantId)
    {
        var tags = new TagList
        {
            { "rule.key", ruleKey },
            { "tenant.id", tenantId.ToString() }
        };

        _evaluationTime.Record(milliseconds, tags);
        _ruleEvaluations.Add(1, tags);
        
        if (matched)
        {
            _ruleMatches.Add(1, tags);
        }
    }

    public void Dispose()
    {
        _meter?.Dispose();
    }
}