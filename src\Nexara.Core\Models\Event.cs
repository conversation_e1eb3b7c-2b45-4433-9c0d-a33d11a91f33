using System.ComponentModel.DataAnnotations;

namespace Nexara.Core.Models;

public class Event
{
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public Guid SessionId { get; set; }
    
    [Required]
    [MaxLength(100)]
    public required string Type { get; set; }
    
    [Required]
    public required string PayloadJson { get; set; }
    
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    
    public virtual Session Session { get; set; } = null!;
}