using Microsoft.Extensions.Caching.Distributed;
using Nexara.Core.Models;
using System.Text.Json;

namespace Nexara.Core.Services;

public class SessionCache : ISessionCache
{
    private readonly IDistributedCache _distributedCache;

    public SessionCache(IDistributedCache distributedCache)
    {
        _distributedCache = distributedCache;
    }

    public async Task<SessionState?> GetSessionStateAsync(Guid sessionId)
    {
        var key = GetCacheKey(sessionId);
        var cachedJson = await _distributedCache.GetStringAsync(key);
        
        if (string.IsNullOrEmpty(cachedJson))
            return null;

        return JsonSerializer.Deserialize<SessionState>(cachedJson);
    }

    public async Task SetSessionStateAsync(Guid sessionId, SessionState state, TimeSpan? ttl = null)
    {
        var key = GetCacheKey(sessionId);
        var json = JsonSerializer.Serialize(state);
        
        var options = new DistributedCacheEntryOptions();
        if (ttl.HasValue)
        {
            options.SetSlidingExpiration(ttl.Value);
        }
        else
        {
            options.SetSlidingExpiration(TimeSpan.FromMinutes(30));
        }

        await _distributedCache.SetStringAsync(key, json, options);
    }

    public async Task RemoveSessionStateAsync(Guid sessionId)
    {
        var key = GetCacheKey(sessionId);
        await _distributedCache.RemoveAsync(key);
    }

    private static string GetCacheKey(Guid sessionId) => $"session_state:{sessionId}";
}