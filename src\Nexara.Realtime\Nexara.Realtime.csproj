<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR" Version="1.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.StackExchangeRedis" Version="9.0.8" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Nexara.Core\Nexara.Core.csproj" />
    <ProjectReference Include="..\Nexara.Rules\Nexara.Rules.csproj" />
    <ProjectReference Include="..\Nexara.ServiceDefaults\Nexara.ServiceDefaults.csproj" />
  </ItemGroup>

</Project>
