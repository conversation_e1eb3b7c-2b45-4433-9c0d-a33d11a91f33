using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Nexara.Core.Data;
using Nexara.Core.Models;
using Nexara.Core.Services;

namespace Nexara.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class SessionsController : ControllerBase
{
    private readonly NexaraDbContext _context;
    private readonly ISessionStateManager _sessionStateManager;
    private readonly ILogger<SessionsController> _logger;

    public SessionsController(
        NexaraDbContext context,
        ISessionStateManager sessionStateManager,
        ILogger<SessionsController> logger)
    {
        _context = context;
        _sessionStateManager = sessionStateManager;
        _logger = logger;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<Session>>> GetSessions()
    {
        var sessions = await _context.Sessions
            .Include(s => s.Survey)
            .OrderByDescending(s => s.StartedAt)
            .ToListAsync();

        return Ok(sessions);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<Session>> GetSession(Guid id)
    {
        var session = await _context.Sessions
            .Include(s => s.Survey)
            .Include(s => s.Messages)
            .Include(s => s.Answers)
            .FirstOrDefaultAsync(s => s.Id == id);

        if (session == null)
        {
            return NotFound();
        }

        return Ok(session);
    }

    [HttpPost]
    public async Task<ActionResult<Session>> CreateSession(CreateSessionRequest request)
    {
        var tenantId = GetTenantId();
        
        // Verify survey exists and is published
        var survey = await _context.Surveys
            .FirstOrDefaultAsync(s => s.Id == request.SurveyId && s.Status == SurveyStatus.Published);

        if (survey == null)
        {
            return BadRequest("Survey not found or not published");
        }

        var session = new Session
        {
            SurveyId = request.SurveyId,
            TenantId = tenantId,
            UserExternalId = request.UserExternalId,
            MetaJson = request.MetaJson
        };

        _context.Sessions.Add(session);
        await _context.SaveChangesAsync();

        // Initialize session state
        await _sessionStateManager.GetStateAsync(session.Id);

        _logger.LogInformation("Created session {SessionId} for survey {SurveyId}", session.Id, request.SurveyId);

        return CreatedAtAction(nameof(GetSession), new { id = session.Id }, session);
    }

    [HttpPost("{id}/complete")]
    public async Task<IActionResult> CompleteSession(Guid id)
    {
        var session = await _context.Sessions.FindAsync(id);
        
        if (session == null)
        {
            return NotFound();
        }

        session.Status = SessionStatus.Completed;
        session.CompletedAt = DateTime.UtcNow;
        session.LastActivityAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        _logger.LogInformation("Completed session {SessionId}", session.Id);

        return NoContent();
    }

    [HttpGet("{id}/state")]
    public async Task<ActionResult<SessionState>> GetSessionState(Guid id)
    {
        try
        {
            var sessionState = await _sessionStateManager.GetStateAsync(id);
            return Ok(sessionState);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving session state for {SessionId}", id);
            return StatusCode(500, "Error retrieving session state");
        }
    }

    [HttpPost("{id}/back")]
    public async Task<IActionResult> NavigateBack(Guid id)
    {
        try
        {
            var success = await _sessionStateManager.TryNavigateBackAsync(id);
            
            if (!success)
            {
                return BadRequest("Cannot navigate back");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error navigating back for session {SessionId}", id);
            return StatusCode(500, "Error navigating back");
        }
    }

    private Guid GetTenantId()
    {
        if (HttpContext.Items.TryGetValue("TenantId", out var tenantIdObj) && 
            tenantIdObj is Guid tenantId)
        {
            return tenantId;
        }

        throw new InvalidOperationException("Tenant context not available");
    }
}

public record CreateSessionRequest(Guid SurveyId, string? UserExternalId = null, string? MetaJson = null);