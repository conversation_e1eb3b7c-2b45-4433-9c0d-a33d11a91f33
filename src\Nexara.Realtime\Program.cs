using Microsoft.EntityFrameworkCore;
using Nexara.Core.Data;
using Nexara.Core.MultiTenancy;
using Nexara.Realtime.Hubs;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddSignalR()
    .AddStackExchangeRedis(builder.Configuration.GetConnectionString("Redis") ?? "localhost:6379", options =>
    {
        options.Configuration.ChannelPrefix = $"nexara_{builder.Environment.EnvironmentName}";
    });

// Database
var connectionString = builder.Configuration.GetConnectionString("DefaultConnection") 
    ?? "Host=localhost;Database=nexara;Username=postgres;Password=password";
builder.Services.AddDbContext<NexaraDbContext>(options =>
    options.UseNpgsql(connectionString));

// Multi-tenancy
builder.Services.AddScoped<ITenantProvider, TenantProvider>();
builder.Services.AddScoped<ITenantResolver, TenantResolver>();

// CORS for SignalR
builder.Services.AddCors(options =>
{
    options.AddPolicy("RealtimeCors", policy =>
    {
        policy.AllowAnyOrigin() // In production, specify allowed origins
              .AllowAnyHeader()
              .AllowAnyMethod();
    });
});

// Logging
builder.Services.AddLogging();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
}

app.UseHttpsRedirection();

app.UseCors("RealtimeCors");

// Multi-tenancy middleware
app.UseMiddleware<TenantContextMiddleware>();

app.UseRouting();

app.MapHub<ChatHub>("/chatHub");

app.Run();
