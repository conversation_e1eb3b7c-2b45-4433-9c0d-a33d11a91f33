using Microsoft.Extensions.Caching.Distributed;
using Nexara.Core.Models;
using Nexara.Rules;
using System.Text.Json;
using Microsoft.EntityFrameworkCore;
using Nexara.Core.Data;

namespace Nexara.Core.Services;

public class SurveyCache : ISurveyCache
{
    private readonly IDistributedCache _distributedCache;
    private readonly IDbContextFactory<NexaraDbContext> _dbContextFactory;
    private readonly IRuleRegistry _ruleRegistry;

    public SurveyCache(
        IDistributedCache distributedCache,
        IDbContextFactory<NexaraDbContext> dbContextFactory,
        IRuleRegistry ruleRegistry)
    {
        _distributedCache = distributedCache;
        _dbContextFactory = dbContextFactory;
        _ruleRegistry = ruleRegistry;
    }

    public async Task<Survey?> GetSurveyAsync(Guid surveyId, int version)
    {
        var key = GetSurveyCacheKey(surveyId, version);
        var cachedJson = await _distributedCache.GetStringAsync(key);
        
        if (!string.IsNullOrEmpty(cachedJson))
        {
            return JsonSerializer.Deserialize<Survey>(cachedJson);
        }

        // Fallback to database
        using var context = await _dbContextFactory.CreateDbContextAsync();
        var survey = await context.Surveys
            .Include(s => s.Nodes)
            .Include(s => s.Edges)
            .Include(s => s.RuleDefinitions)
            .AsNoTracking()
            .FirstOrDefaultAsync(s => s.Id == surveyId && s.Version == version);

        if (survey != null)
        {
            // Cache for 1 hour
            var options = new DistributedCacheEntryOptions
            {
                SlidingExpiration = TimeSpan.FromHours(1)
            };
            
            await _distributedCache.SetStringAsync(key, JsonSerializer.Serialize(survey), options);
        }

        return survey;
    }

    public async Task<IReadOnlyList<INexaraRule>> GetCompiledRulesAsync(Guid surveyId)
    {
        var key = GetRulesCacheKey(surveyId);
        var cachedJson = await _distributedCache.GetStringAsync(key);
        
        if (!string.IsNullOrEmpty(cachedJson))
        {
            var ruleKeys = JsonSerializer.Deserialize<List<string>>(cachedJson) ?? new List<string>();
            var rules = new List<INexaraRule>();
            
            foreach (var ruleKey in ruleKeys)
            {
                var rule = _ruleRegistry.GetRule(ruleKey);
                if (rule != null)
                {
                    rules.Add(rule);
                }
            }
            
            return rules;
        }

        // For now, return all rules from registry
        // In the future, this would filter by survey-specific rules
        var allRules = _ruleRegistry.GetRules().ToList();
        var allRuleKeys = allRules.Select(r => r.Key).ToList();
        
        // Cache rule keys for 1 hour
        var options = new DistributedCacheEntryOptions
        {
            SlidingExpiration = TimeSpan.FromHours(1)
        };
        
        await _distributedCache.SetStringAsync(key, JsonSerializer.Serialize(allRuleKeys), options);
        
        return allRules;
    }

    public async Task InvalidateSurveyAsync(Guid surveyId)
    {
        // Remove all cached versions and rules for this survey
        await _distributedCache.RemoveAsync(GetRulesCacheKey(surveyId));
        
        // Note: In a real implementation, we'd also need to remove all version-specific survey caches
        // This could be done by maintaining a list of cached versions or using cache tagging
    }

    private static string GetSurveyCacheKey(Guid surveyId, int version) => $"survey:{surveyId}:v{version}";
    private static string GetRulesCacheKey(Guid surveyId) => $"survey_rules:{surveyId}";
}