using System.ComponentModel.DataAnnotations;

namespace Nexara.Core.Models;

public class RuleDefinition
{
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public Guid SurveyId { get; set; }
    
    [Required]
    [MaxLength(100)]
    public required string RuleKey { get; set; }
    
    [Required]
    [MaxLength(100)]
    public required string RuleType { get; set; }
    
    [Required]
    public required string DefinitionJson { get; set; }
    
    public byte[]? CompiledAssembly { get; set; }
    
    public int Version { get; set; } = 1;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    public virtual Survey Survey { get; set; } = null!;
}