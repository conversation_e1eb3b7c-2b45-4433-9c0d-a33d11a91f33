import axios, { AxiosResponse } from 'axios'
import type { 
  ApiResponse, 
  PaginatedResponse, 
  Bot, 
  CreateBotRequest, 
  UpdateBotRequest,
  Conversation,
  Survey,
  CreateConversationRequest,
  ConversationResponse,
  MessageResponse,
  SendMessageRequest,
  ChatConnectionInfo
} from '../types/api'

const API_BASE_URL = import.meta.env.VITE_API_URL || '/api'

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor for auth token
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('auth_token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  
  // Add tenant header
  const tenantId = localStorage.getItem('tenant_id')
  if (tenantId) {
    config.headers['X-Tenant-ID'] = tenantId
  }
  
  return config
})

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('auth_token')
      localStorage.removeItem('tenant_id')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

export const botsApi = {
  getAll: (): Promise<AxiosResponse<ApiResponse<PaginatedResponse<Bot>>>> =>
    apiClient.get('/bots'),
    
  getById: (id: string): Promise<AxiosResponse<ApiResponse<Bot>>> =>
    apiClient.get(`/bots/${id}`),
    
  create: (data: CreateBotRequest): Promise<AxiosResponse<ApiResponse<Bot>>> =>
    apiClient.post('/bots', data),
    
  update: (id: string, data: UpdateBotRequest): Promise<AxiosResponse<ApiResponse<Bot>>> =>
    apiClient.put(`/bots/${id}`, data),
    
  delete: (id: string): Promise<AxiosResponse<ApiResponse<void>>> =>
    apiClient.delete(`/bots/${id}`),
}

export const conversationsApi = {
  getAll: (): Promise<AxiosResponse<ApiResponse<PaginatedResponse<Conversation>>>> =>
    apiClient.get('/conversations'),
    
  getById: (id: string): Promise<AxiosResponse<ApiResponse<Conversation>>> =>
    apiClient.get(`/conversations/${id}`),
}

export const surveysApi = {
  getAll: (): Promise<AxiosResponse<ApiResponse<PaginatedResponse<Survey>>>> =>
    apiClient.get('/surveys'),
    
  getById: (id: string): Promise<AxiosResponse<ApiResponse<Survey>>> =>
    apiClient.get(`/surveys/${id}`),
}

export const chatApi = {
  // Conversation management
  createConversation: (data: CreateConversationRequest): Promise<AxiosResponse<ApiResponse<ConversationResponse>>> =>
    apiClient.post('/conversations', data),
    
  getConversation: (id: string): Promise<AxiosResponse<ApiResponse<ConversationResponse>>> =>
    apiClient.get(`/conversations/${id}`),
    
  endConversation: (id: string): Promise<AxiosResponse<ApiResponse<void>>> =>
    apiClient.post(`/conversations/${id}/end`),
    
  // Message management
  getMessages: (conversationId: string): Promise<AxiosResponse<ApiResponse<PaginatedResponse<MessageResponse>>>> =>
    apiClient.get(`/conversations/${conversationId}/messages`),
    
  sendMessage: (data: SendMessageRequest): Promise<AxiosResponse<ApiResponse<MessageResponse>>> =>
    apiClient.post('/messages', data),
    
  // Chat connection info
  getConnectionInfo: (): Promise<AxiosResponse<ApiResponse<ChatConnectionInfo>>> =>
    apiClient.get('/chat/connection-info'),
}

export default apiClient