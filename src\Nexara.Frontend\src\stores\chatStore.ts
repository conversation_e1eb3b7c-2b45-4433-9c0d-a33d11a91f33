import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { 
  ChatState, 
  ChatConversation, 
  ChatMessage 
} from '../types/chat'

interface ChatStore extends ChatState {
  // Actions
  addConversation: (conversation: ChatConversation) => void
  removeConversation: (conversationId: string) => void
  setActiveConversation: (conversationId: string | null) => void
  addMessage: (conversationId: string, message: ChatMessage) => void
  updateMessage: (conversationId: string, messageId: string, updates: Partial<ChatMessage>) => void
  setConnectionStatus: (status: ChatState['connectionStatus']) => void
  setIsConnected: (isConnected: boolean) => void
  setIsTyping: (isTyping: boolean) => void
  clearConversations: () => void
  updateConversationActivity: (conversationId: string) => void
  
  // Getters
  getActiveConversation: () => ChatConversation | null
  getConversationById: (id: string) => ChatConversation | null
  getConversationMessages: (conversationId: string) => ChatMessage[]
}

export const useChatStore = create<ChatStore>()(
  devtools(
    (set, get) => ({
      // Initial state
      conversations: [],
      activeConversationId: null,
      isConnected: false,
      isTyping: false,
      connectionStatus: 'disconnected',

      // Actions
      addConversation: (conversation) =>
        set((state) => ({
          conversations: [...state.conversations, conversation],
          activeConversationId: state.activeConversationId || conversation.id
        }), false, 'addConversation'),

      removeConversation: (conversationId) =>
        set((state) => ({
          conversations: state.conversations.filter(c => c.id !== conversationId),
          activeConversationId: 
            state.activeConversationId === conversationId 
              ? state.conversations.find(c => c.id !== conversationId)?.id || null
              : state.activeConversationId
        }), false, 'removeConversation'),

      setActiveConversation: (conversationId) =>
        set({ activeConversationId: conversationId }, false, 'setActiveConversation'),

      addMessage: (conversationId, message) =>
        set((state) => ({
          conversations: state.conversations.map(conv =>
            conv.id === conversationId
              ? {
                  ...conv,
                  messages: [...conv.messages, message],
                  lastActivity: new Date()
                }
              : conv
          )
        }), false, 'addMessage'),

      updateMessage: (conversationId, messageId, updates) =>
        set((state) => ({
          conversations: state.conversations.map(conv =>
            conv.id === conversationId
              ? {
                  ...conv,
                  messages: conv.messages.map(msg =>
                    msg.id === messageId ? { ...msg, ...updates } : msg
                  )
                }
              : conv
          )
        }), false, 'updateMessage'),

      setConnectionStatus: (status) =>
        set({ connectionStatus: status }, false, 'setConnectionStatus'),

      setIsConnected: (isConnected) =>
        set({ isConnected }, false, 'setIsConnected'),

      setIsTyping: (isTyping) =>
        set({ isTyping }, false, 'setIsTyping'),

      clearConversations: () =>
        set({ 
          conversations: [], 
          activeConversationId: null 
        }, false, 'clearConversations'),

      updateConversationActivity: (conversationId) =>
        set((state) => ({
          conversations: state.conversations.map(conv =>
            conv.id === conversationId
              ? { ...conv, lastActivity: new Date() }
              : conv
          )
        }), false, 'updateConversationActivity'),

      // Getters
      getActiveConversation: () => {
        const state = get()
        return state.conversations.find(c => c.id === state.activeConversationId) || null
      },

      getConversationById: (id) => {
        const state = get()
        return state.conversations.find(c => c.id === id) || null
      },

      getConversationMessages: (conversationId) => {
        const state = get()
        const conversation = state.conversations.find(c => c.id === conversationId)
        return conversation?.messages || []
      }
    }),
    {
      name: 'chat-store'
    }
  )
)