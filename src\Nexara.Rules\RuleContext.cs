namespace Nexara.Rules;

public sealed class RuleContext
{
    public required Guid SessionId { get; init; }
    public required string Locale { get; init; }
    public required IReadOnlyDictionary<string, object?> Answers { get; init; }
    public required IDictionary<string, double> Scores { get; init; }
    public required ISet<string> Tags { get; init; }
    public required IDictionary<string, object?> Meta { get; init; }
}