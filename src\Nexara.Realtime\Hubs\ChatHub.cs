using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Nexara.Core.Data;
using Nexara.Core.Models;
using Nexara.Core.Security;
using System.Text.Json;

namespace Nexara.Realtime.Hubs;

public class ChatHub : Hub
{
    private readonly NexaraDbContext _context;
    private readonly ITokenValidator _tokenValidator;
    private readonly ILogger<ChatHub> _logger;

    public ChatHub(NexaraDbContext context, ITokenValidator tokenValidator, ILogger<ChatHub> logger)
    {
        _context = context;
        _tokenValidator = tokenValidator;
        _logger = logger;
    }

    public override async Task OnConnectedAsync()
    {
        var httpContext = Context.GetHttpContext();
        var origin = httpContext?.Request.Headers["Origin"].ToString();
        var token = httpContext?.Request.Query["token"].FirstOrDefault();
        var conversationId = httpContext?.Request.Query["conversationId"].FirstOrDefault();
        
        // Validate embed token
        if (string.IsNullOrEmpty(token))
        {
            _logger.LogWarning("SignalR connection attempted without token");
            Context.Abort();
            return;
        }

        var tokenData = await _tokenValidator.ValidateAsync(token);
        if (tokenData == null)
        {
            _logger.LogWarning("SignalR connection attempted with invalid token");
            Context.Abort();
            return;
        }

        // Validate origin matches allowed origins in token
        if (!string.IsNullOrEmpty(origin) && !tokenData.AllowedOrigins.Contains(origin))
        {
            _logger.LogWarning("SignalR connection from unauthorized origin: {Origin}", origin);
            Context.Abort();
            return;
        }

        // If conversationId provided, verify it belongs to the bot
        if (!string.IsNullOrEmpty(conversationId) && Guid.TryParse(conversationId, out var conversationGuid))
        {
            var conversation = await _context.Conversations
                .FirstOrDefaultAsync(c => c.Id == conversationGuid && 
                                       c.BotId == tokenData.BotId && 
                                       c.TenantId == tokenData.TenantId && 
                                       c.Status == ConversationStatus.Active);

            if (conversation == null)
            {
                _logger.LogWarning("SignalR connection attempted for invalid or inactive conversation {ConversationId}", conversationGuid);
                Context.Abort();
                return;
            }

            Context.Items["ConversationId"] = conversationGuid;
            await Groups.AddToGroupAsync(Context.ConnectionId, $"conversation-{conversationGuid}");
        }

        // Store validated context
        Context.Items["TenantId"] = tokenData.TenantId;
        Context.Items["BotId"] = tokenData.BotId;
        Context.Items["Origin"] = origin;

        // Add to bot group for general bot communications
        await Groups.AddToGroupAsync(Context.ConnectionId, $"bot-{tokenData.BotId}");

        _logger.LogDebug("SignalR connection established for bot {BotId}", tokenData.BotId);
        
        await base.OnConnectedAsync();
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        if (Context.Items.TryGetValue("ConversationId", out var conversationIdObj) && conversationIdObj is Guid conversationId)
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"conversation-{conversationId}");
            _logger.LogDebug("SignalR connection disconnected for conversation {ConversationId}", conversationId);
        }

        if (Context.Items.TryGetValue("BotId", out var botIdObj) && botIdObj is Guid botId)
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"bot-{botId}");
        }

        await base.OnDisconnectedAsync(exception);
    }

    public async Task SendMessage(string conversationIdStr, object messageData)
    {
        if (!Guid.TryParse(conversationIdStr, out var conversationId))
        {
            await Clients.Caller.SendAsync("Error", "Invalid conversation ID");
            return;
        }

        if (!Context.Items.TryGetValue("BotId", out var botIdObj) || botIdObj is not Guid botId)
        {
            await Clients.Caller.SendAsync("Error", "Bot context not found");
            return;
        }

        try
        {
            // Verify conversation belongs to the bot
            var conversation = await _context.Conversations
                .FirstOrDefaultAsync(c => c.Id == conversationId && c.BotId == botId);

            if (conversation == null)
            {
                await Clients.Caller.SendAsync("Error", "Conversation not found");
                return;
            }

            var message = new Message
            {
                ConversationId = conversationId,
                Role = MessageRole.User,
                Content = JsonSerializer.Serialize(messageData),
                Status = MessageStatus.Sent
            };

            _context.Messages.Add(message);

            // Update conversation activity
            conversation.LastActivityAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            // Broadcast to conversation group
            await Clients.Group($"conversation-{conversationId}").SendAsync("ReceiveMessage", new
            {
                Id = message.Id,
                Role = message.Role.ToString(),
                Content = messageData,
                CreatedAt = message.CreatedAt
            });

            _logger.LogDebug("Message sent for conversation {ConversationId}", conversationId);

            // TODO: Process message through rules engine and generate bot response
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending message for conversation {ConversationId}", conversationId);
            await Clients.Caller.SendAsync("Error", "Failed to send message");
        }
    }

    public async Task AckMessage(string messageId)
    {
        if (!Guid.TryParse(messageId, out var messageGuid))
        {
            await Clients.Caller.SendAsync("Error", "Invalid message ID");
            return;
        }

        try
        {
            var message = await _context.Messages.FindAsync(messageGuid);
            if (message != null)
            {
                message.Status = MessageStatus.Delivered;
                message.DeliveredAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();

                _logger.LogDebug("Message {MessageId} acknowledged", messageGuid);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error acknowledging message {MessageId}", messageGuid);
        }
    }

    public async Task MarkMessageRead(string messageId)
    {
        if (!Guid.TryParse(messageId, out var messageGuid))
        {
            await Clients.Caller.SendAsync("Error", "Invalid message ID");
            return;
        }

        try
        {
            var message = await _context.Messages.FindAsync(messageGuid);
            if (message != null)
            {
                message.Status = MessageStatus.Read;
                message.ReadAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();

                _logger.LogDebug("Message {MessageId} marked as read", messageGuid);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error marking message as read {MessageId}", messageGuid);
        }
    }

    public async Task JoinConversation(string conversationIdStr)
    {
        if (!Guid.TryParse(conversationIdStr, out var conversationId))
        {
            await Clients.Caller.SendAsync("Error", "Invalid conversation ID");
            return;
        }

        if (!Context.Items.TryGetValue("BotId", out var botIdObj) || botIdObj is not Guid botId)
        {
            await Clients.Caller.SendAsync("Error", "Bot context not found");
            return;
        }

        // Verify conversation exists and belongs to the bot
        var conversation = await _context.Conversations
            .FirstOrDefaultAsync(c => c.Id == conversationId && 
                               c.BotId == botId && 
                               c.Status == ConversationStatus.Active);

        if (conversation == null)
        {
            await Clients.Caller.SendAsync("Error", "Conversation not found or inactive");
            return;
        }

        Context.Items["ConversationId"] = conversationId;

        await Groups.AddToGroupAsync(Context.ConnectionId, $"conversation-{conversationId}");
        
        _logger.LogDebug("Connection joined conversation {ConversationId}", conversationId);
        await Clients.Caller.SendAsync("JoinedConversation", conversationId);
    }
}