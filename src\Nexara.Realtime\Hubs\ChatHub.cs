using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Nexara.Core.Data;
using Nexara.Core.Models;
using System.Text.Json;

namespace Nexara.Realtime.Hubs;

public class ChatHub : Hub
{
    private readonly NexaraDbContext _context;
    private readonly ILogger<ChatHub> _logger;

    public ChatHub(NexaraDbContext context, ILogger<ChatHub> logger)
    {
        _context = context;
        _logger = logger;
    }

    public override async Task OnConnectedAsync()
    {
        var httpContext = Context.GetHttpContext();
        var sessionId = httpContext?.Request.Query["sessionId"].FirstOrDefault();
        
        if (string.IsNullOrEmpty(sessionId) || !Guid.TryParse(sessionId, out var sessionGuid))
        {
            _logger.LogWarning("SignalR connection attempted without valid sessionId");
            Context.Abort();
            return;
        }

        // Verify session exists and is active
        var session = await _context.Sessions
            .FirstOrDefaultAsync(s => s.Id == sessionGuid && s.Status == SessionStatus.Active);

        if (session == null)
        {
            _logger.LogWarning("SignalR connection attempted for invalid or inactive session {SessionId}", sessionGuid);
            Context.Abort();
            return;
        }

        // Store session context
        Context.Items["SessionId"] = sessionGuid;
        Context.Items["TenantId"] = session.TenantId;

        // Join session group
        await Groups.AddToGroupAsync(Context.ConnectionId, $"session-{sessionGuid}");

        _logger.LogDebug("SignalR connection established for session {SessionId}", sessionGuid);
        
        await base.OnConnectedAsync();
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        if (Context.Items.TryGetValue("SessionId", out var sessionIdObj) && sessionIdObj is Guid sessionId)
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"session-{sessionId}");
            _logger.LogDebug("SignalR connection disconnected for session {SessionId}", sessionId);
        }

        await base.OnDisconnectedAsync(exception);
    }

    public async Task SendMessage(object messageData)
    {
        if (!Context.Items.TryGetValue("SessionId", out var sessionIdObj) || sessionIdObj is not Guid sessionId)
        {
            await Clients.Caller.SendAsync("Error", "Session not found");
            return;
        }

        try
        {
            var message = new Message
            {
                SessionId = sessionId,
                Role = MessageRole.User,
                ContentJson = JsonSerializer.Serialize(messageData),
                Status = MessageStatus.Sent
            };

            _context.Messages.Add(message);

            // Update session activity
            var session = await _context.Sessions.FindAsync(sessionId);
            if (session != null)
            {
                session.LastActivityAt = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();

            // Broadcast to session group
            await Clients.Group($"session-{sessionId}").SendAsync("ReceiveMessage", new
            {
                Id = message.Id,
                Role = message.Role.ToString(),
                Content = messageData,
                CreatedAt = message.CreatedAt
            });

            _logger.LogDebug("Message sent for session {SessionId}", sessionId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending message for session {SessionId}", sessionId);
            await Clients.Caller.SendAsync("Error", "Failed to send message");
        }
    }

    public async Task AckMessage(string messageId)
    {
        if (!Guid.TryParse(messageId, out var messageGuid))
        {
            await Clients.Caller.SendAsync("Error", "Invalid message ID");
            return;
        }

        try
        {
            var message = await _context.Messages.FindAsync(messageGuid);
            if (message != null)
            {
                message.Status = MessageStatus.Delivered;
                message.DeliveredAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();

                _logger.LogDebug("Message {MessageId} acknowledged", messageGuid);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error acknowledging message {MessageId}", messageGuid);
        }
    }

    public async Task MarkMessageRead(string messageId)
    {
        if (!Guid.TryParse(messageId, out var messageGuid))
        {
            await Clients.Caller.SendAsync("Error", "Invalid message ID");
            return;
        }

        try
        {
            var message = await _context.Messages.FindAsync(messageGuid);
            if (message != null)
            {
                message.Status = MessageStatus.Read;
                message.ReadAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();

                _logger.LogDebug("Message {MessageId} marked as read", messageGuid);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error marking message as read {MessageId}", messageGuid);
        }
    }

    public async Task JoinSession(string sessionIdStr)
    {
        if (!Guid.TryParse(sessionIdStr, out var sessionId))
        {
            await Clients.Caller.SendAsync("Error", "Invalid session ID");
            return;
        }

        // Verify session exists and is active
        var session = await _context.Sessions
            .FirstOrDefaultAsync(s => s.Id == sessionId && s.Status == SessionStatus.Active);

        if (session == null)
        {
            await Clients.Caller.SendAsync("Error", "Session not found or inactive");
            return;
        }

        Context.Items["SessionId"] = sessionId;
        Context.Items["TenantId"] = session.TenantId;

        await Groups.AddToGroupAsync(Context.ConnectionId, $"session-{sessionId}");
        
        _logger.LogDebug("Connection joined session {SessionId}", sessionId);
        await Clients.Caller.SendAsync("JoinedSession", sessionId);
    }
}