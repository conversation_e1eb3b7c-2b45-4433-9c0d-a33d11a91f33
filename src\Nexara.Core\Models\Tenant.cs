using System.ComponentModel.DataAnnotations;

namespace Nexara.Core.Models;

public class Tenant
{
    public Guid Id { get; set; } = Guid.NewGuid();
    
    [Required]
    [MaxLength(255)]
    public required string Name { get; set; }
    
    [Required]
    [MaxLength(100)]
    public required string Plan { get; set; }
    
    public string? SettingsJson { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    public virtual ICollection<User> Users { get; set; } = new List<User>();
    public virtual ICollection<Survey> Surveys { get; set; } = new List<Survey>();
    public virtual ICollection<Session> Sessions { get; set; } = new List<Session>();
}