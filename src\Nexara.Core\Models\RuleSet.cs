using System.ComponentModel.DataAnnotations;

namespace Nexara.Core.Models;

public class RuleSet
{
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public Guid BotId { get; set; }
    
    [Required]
    [MaxLength(255)]
    public required string Name { get; set; }
    
    public int Version { get; set; } = 1;
    
    public bool IsActive { get; set; } = false;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    // Navigation properties
    public virtual Bot Bot { get; set; } = null!;
    public virtual ICollection<PromptRule> PromptRules { get; set; } = new List<PromptRule>();
}