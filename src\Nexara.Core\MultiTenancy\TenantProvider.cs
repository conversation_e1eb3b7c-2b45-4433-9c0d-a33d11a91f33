using Nexara.Core.Data;

namespace Nexara.Core.MultiTenancy;

public class TenantProvider : ITenantProvider
{
    private Guid? _tenantId;
    private string? _tenantName;

    public Guid? GetTenantId() => _tenantId;
    
    public void SetTenantId(Guid tenantId) => _tenantId = tenantId;
    
    public string? GetTenantName() => _tenantName;
    
    public void SetTenantName(string tenantName) => _tenantName = tenantName;
}