// Chat-specific types for the frontend

export interface ChatMessage {
  id: string
  content: string
  isFromUser: boolean
  timestamp: Date
  sender: string
  messageType: 'text' | 'system' | 'typing'
}

export interface ChatConversation {
  id: string
  botId: string
  sessionId: string
  messages: ChatMessage[]
  isActive: boolean
  startedAt: Date
  lastActivity: Date
}

export interface ChatState {
  conversations: ChatConversation[]
  activeConversationId: string | null
  isConnected: boolean
  isTyping: boolean
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error'
}

export interface SendMessagePayload {
  conversationId: string
  content: string
  messageType?: string
}

export interface ReceiveMessagePayload {
  conversationId: string
  message: ChatMessage
}

export interface TypingIndicatorPayload {
  conversationId: string
  isTyping: boolean
  userId?: string
}

export interface ConnectionState {
  isConnected: boolean
  reconnectAttempts: number
  lastError?: string
}

// Mock data interfaces for testing
export interface MockBot {
  id: string
  name: string
  description: string
  avatar?: string
}

export interface MockConversationScenario {
  id: string
  name: string
  description: string
  bot: MockBot
  messages: ChatMessage[]
}