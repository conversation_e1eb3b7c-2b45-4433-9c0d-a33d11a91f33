# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Architecture Overview

Nexara is a SaaS platform for intelligent chatbots with rule-based responses and integrated survey capabilities. The platform uses a microservices architecture with a .NET 9 backend and React frontend, featuring the following services:

- **Nexara.Api** - REST endpoints for bots, conversations, rules, webhooks, and analytics
- **Nexara.Realtime** - SignalR hub for real-time chat sessions 
- **Nexara.ChatEngine** - Core conversation engine that matches prompts to rules and manages conversation flow
- **Nexara.Rules** - C# rule interfaces and implementations for prompt matching and response generation
- **Nexara.Worker** - Background jobs for scheduled surveys, timeouts, and webhook retries
- **Nexara.Connectors** - External integrations (Slack/Teams, CRM sync, webhook delivery)
- **Nexara.Core** - Shared models, data access, multi-tenancy, and security components
- **Nexara.Frontend** - React TypeScript frontend for admin dashboard and bot management

## Development Commands

### Build and Run
```bash
# Start all services using .NET Aspire (includes frontend dev server)
dotnet run -p src/Nexara.AppHost

# Build entire solution (includes frontend)
dotnet build Nexara.sln

# Build specific backend project
dotnet build src/Nexara.Api/Nexara.Api.csproj

# Frontend development (alternative to Aspire)
cd src/Nexara.Frontend
npm install
npm run dev

# Build frontend for production
cd src/Nexara.Frontend
npm run build

# Run tests (when test projects exist)
dotnet test
```

### Database Operations
```bash
# Add migration
dotnet ef migrations add <MigrationName> -p src/Nexara.Core -s src/Nexara.Api

# Update database
dotnet ef database update -p src/Nexara.Core -s src/Nexara.Api
```

## Core Concepts

### Rules System
The platform uses C# rules that implement `IPromptRule` for matching user prompts and generating responses:

- **MatchConfidence()** - Returns 0.0 to 1.0 confidence score for prompt matching
- **GenerateResponseAsync()** - Produces rule response with message, actions, and state updates
- Rules can trigger surveys, update conversation state, send webhooks, and control conversation flow

### Multi-Tenancy
All data is tenant-isolated using:
- `TenantContextMiddleware` for request-level tenant resolution
- EF Core global query filters on tenant-scoped entities
- Row-level security with `tenant_id` on all relevant tables

### Conversation State Management
- **ConversationState** - Versioned state with optimistic concurrency control
- Stores conversation data, navigation history, scores, tags, and active surveys
- Cached in Redis for performance

### Security Architecture
- JWT embed tokens for widget authentication with origin validation
- CSP headers prevent unauthorized widget embedding
- Rate limiting per embed token (100 requests/minute default)
- SignalR connections validate token and origin

## Key File Locations

### Backend (.NET)
- Core models: `src/Nexara.Core/Models/`
- Database context: `src/Nexara.Core/Data/NexaraDbContext.cs`
- Multi-tenancy: `src/Nexara.Core/MultiTenancy/`
- Rule interfaces: `src/Nexara.Rules/INexaraRule.cs`, `src/Nexara.Rules/IRuleEvaluator.cs`
- Example rules: `src/Nexara.Rules/Examples/`
- API endpoints: `src/Nexara.Api/Controllers/`
- Authentication and middleware: `src/Nexara.Api/Program.cs`
- SignalR hub: `src/Nexara.Realtime/Hubs/ChatHub.cs`

### Frontend (React)
- Main application: `src/Nexara.Frontend/src/App.tsx`
- Pages and components: `src/Nexara.Frontend/src/pages/`, `src/Nexara.Frontend/src/components/`
- API client and services: `src/Nexara.Frontend/src/services/`
- TypeScript types: `src/Nexara.Frontend/src/types/`
- Configuration: `src/Nexara.Frontend/vite.config.ts`

## Development Patterns

### Adding New Rules
1. Create class implementing `IPromptRule` in `src/Nexara.Rules/`
2. Implement `MatchConfidence()` with keyword/pattern matching logic
3. Implement `GenerateResponseAsync()` with response generation
4. Register rule in DI container via `ServiceCollectionExtensions.cs`

### Adding New API Endpoints
1. Create controller in `src/Nexara.Api/Controllers/`
2. Use `[TenantScoped]` attribute for tenant-isolated endpoints
3. Follow existing patterns for authentication and validation

### Database Changes
1. Update models in `src/Nexara.Core/Models/`
2. Update `NexaraDbContext.cs` with entity configuration
3. Add migration and update database

## Testing Strategy

The platform uses multiple testing approaches:
- **Unit tests** - For rule logic and business components
- **Integration tests** - For API endpoints and database operations  
- **Property-based tests** - For rule determinism and edge cases
- **Load tests** - For SignalR and real-time performance

## Infrastructure Dependencies

### Required Services
- **PostgreSQL** - Primary database with row-level security
- **Redis** - Caching, session state, and SignalR backplane  
- **Seq** - Structured logging and observability

### Development Setup
The `Nexara.AppHost` project uses .NET Aspire to orchestrate all services and dependencies locally. Running `dotnet run -p src/Nexara.AppHost` starts:
- PostgreSQL with data volume
- Redis with data volume  
- Seq for log aggregation
- All application services with proper configuration
- React frontend development server with hot reload

## Deployment Architecture

### Production Deployment
- **Backend**: Deployed to Railway using `BackendDockerfile`
- **Frontend**: Deployed to Vercel with automatic builds from `src/Nexara.Frontend/`
- **Database**: Railway managed PostgreSQL
- **Cache**: Railway managed Redis

### Configuration Files
- `railway.json` - Railway deployment configuration
- `src/Nexara.Frontend/vercel.json` - Vercel deployment configuration
- `BackendDockerfile` - Multi-stage Docker build for .NET services

### Environment Variables
- **VITE_API_URL** - Frontend API base URL (set in Vercel)
- **VITE_WS_URL** - WebSocket/SignalR endpoint (set in Vercel)
- Backend services use Railway's managed database connection strings

## Observability

### Logging
- Structured logging with Serilog
- Tenant-scoped logging contexts
- Correlation IDs for request tracking

### Metrics  
- OpenTelemetry for distributed tracing
- Custom metrics for rule evaluation performance
- Conversation flow and rule match rate tracking

### Monitoring
- Rule evaluation timing and confidence scores
- Message delivery lifecycle tracking  
- Rate limiting and security violation detection