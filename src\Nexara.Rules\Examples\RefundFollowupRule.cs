namespace Nexara.Rules.Examples;

public sealed class RefundRequestRule : IPromptRule
{
    public string Key => "refund-request";
    public int Priority => 80;

    public double MatchConfidence(PromptContext context)
    {
        var prompt = context.UserPrompt.ToLowerInvariant();
        
        // Look for refund-related keywords
        var refundKeywords = new[] { "refund", "return", "money back", "cancel order", "get my money" };
        var urgencyKeywords = new[] { "urgent", "immediately", "asap", "right now", "quickly" };
        
        double confidence = 0.0;
        
        // Base confidence if refund keywords are found
        if (refundKeywords.Any(keyword => prompt.Contains(keyword)))
        {
            confidence = 0.7;
        }
        
        // Increase confidence if urgency is expressed
        if (urgencyKeywords.Any(keyword => prompt.Contains(keyword)))
        {
            confidence += 0.2;
        }
        
        // Slight increase if user has previous refund history
        if (context.State.Tags.Contains("previous_refund"))
        {
            confidence += 0.1;
        }
        
        return Math.Min(confidence, 1.0);
    }

    public async Task<RuleResponse> GenerateResponseAsync(PromptContext context)
    {
        var response = new RuleResponse
        {
            Message = "I understand you're looking for a refund. Let me help you with that process. Can you please provide your order number so I can look into this for you?",
            AddTags = new List<string> { "refund_request", "high_priority" },
            UpdateState = new Dictionary<string, object>
            {
                ["intent"] = "refund_request",
                ["needs_order_number"] = true
            },
            TriggerWebhook = new WebhookTrigger
            {
                Url = "https://api.example.com/escalate-billing",
                Payload = new Dictionary<string, object> { ["priority"] = "high", ["type"] = "refund" }
            },
            NextAction = NextAction.WaitForResponse
        };
        
        return await Task.FromResult(response);
    }
}