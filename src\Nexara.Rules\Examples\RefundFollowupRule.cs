namespace Nexara.Rules.Examples;

public sealed class RefundFollowupRule : INexaraRule
{
    public string Key => "refund-followup";
    public int Priority => 80;

    public bool Applies(RuleContext ctx)
    {
        var q1 = ctx.Answers.TryGetValue("q1", out var a1) ? a1 as string : null;
        var feedback = ctx.Answers.TryGetValue("feedback", out var fb) ? fb?.ToString() : null;
        var age = ctx.Meta.TryGetValue("age", out var mAge) && double.TryParse(mAge?.ToString(), out var nAge) ? nAge : (double?)null;

        return q1 == "Yes"
            && (feedback?.IndexOf("refund", StringComparison.OrdinalIgnoreCase) >= 0)
            && age is > 34;
    }

    public RuleAction Evaluate(RuleContext ctx) => new(GotoNodeId: "q_refund_reason");
}