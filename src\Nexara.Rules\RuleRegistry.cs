using Microsoft.Extensions.DependencyInjection;
using System.Collections.Concurrent;

namespace Nexara.Rules;

public class RuleRegistry : IRuleRegistry
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ConcurrentDictionary<string, Type> _ruleTypes = new();
    private readonly ConcurrentDictionary<string, INexaraRule> _ruleInstances = new();

    public RuleRegistry(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public void RegisterRule<T>() where T : class, INexaraRule
    {
        var rule = _serviceProvider.GetService<T>() ?? Activator.CreateInstance<T>();
        RegisterRule(rule);
        _ruleTypes[rule.Key] = typeof(T);
    }

    public void RegisterRule(INexaraRule rule)
    {
        _ruleInstances[rule.Key] = rule;
    }

    public IEnumerable<INexaraRule> GetRules()
    {
        return _ruleInstances.Values;
    }

    public IEnumerable<INexaraRule> GetRulesForSurvey(Guid surveyId, Guid tenantId)
    {
        // In the future, this could filter rules based on survey configuration
        // For now, return all registered rules
        return GetRules();
    }

    public INexaraRule? GetRule(string key)
    {
        _ruleInstances.TryGetValue(key, out var rule);
        return rule;
    }
}