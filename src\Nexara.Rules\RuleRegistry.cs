using Microsoft.Extensions.DependencyInjection;
using System.Collections.Concurrent;

namespace Nexara.Rules;

public class RuleRegistry : IRuleRegistry
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ConcurrentDictionary<string, Type> _ruleTypes = new();
    private readonly ConcurrentDictionary<string, IPromptRule> _ruleInstances = new();

    public RuleRegistry(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public void RegisterRule<T>() where T : class, IPromptRule
    {
        var rule = _serviceProvider.GetService<T>() ?? Activator.CreateInstance<T>();
        RegisterRule(rule);
        _ruleTypes[rule.Key] = typeof(T);
    }

    public void RegisterRule(IPromptRule rule)
    {
        _ruleInstances[rule.Key] = rule;
    }

    public IEnumerable<IPromptRule> GetRules()
    {
        return _ruleInstances.Values;
    }

    public IEnumerable<IPromptRule> GetRulesForBot(Guid botId, Guid tenantId)
    {
        // In the future, this could filter rules based on bot configuration
        // For now, return all registered rules
        return GetRules();
    }

    public IPromptRule? GetRule(string key)
    {
        _ruleInstances.TryGetValue(key, out var rule);
        return rule;
    }
}