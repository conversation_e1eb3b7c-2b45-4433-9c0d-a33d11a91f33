using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using Nexara.Core.Data;
using Nexara.Core.Models;
using System.Text.Json;

namespace Nexara.Core.Services;

public class ConversationStateManager : IConversationStateManager
{
    private readonly NexaraDbContext _context;
    private readonly IDistributedCache _cache;

    public ConversationStateManager(NexaraDbContext context, IDistributedCache cache)
    {
        _context = context;
        _cache = cache;
    }

    public async Task<ConversationState> GetStateAsync(Guid conversationId)
    {
        // First check cache
        var cacheKey = $"conversation_state:{conversationId}";
        var cachedState = await _cache.GetStringAsync(cacheKey);
        
        if (!string.IsNullOrEmpty(cachedState))
        {
            var cached = JsonSerializer.Deserialize<ConversationState>(cachedState);
            if (cached != null)
                return cached;
        }

        // Get from database
        var state = await _context.ConversationStates
            .Where(s => s.ConversationId == conversationId)
            .OrderByDescending(s => s.Version)
            .FirstOrDefaultAsync();

        if (state == null)
        {
            // Create initial state
            state = new ConversationState
            {
                ConversationId = conversationId,
                Version = 1,
                StateJson = JsonSerializer.Serialize(new
                {
                    Data = new Dictionary<string, object>(),
                    NavigationHistory = new Stack<string>(),
                    Scores = new Dictionary<string, double>(),
                    Tags = new HashSet<string>(),
                    LastActivityUtc = DateTime.UtcNow,
                    ActiveSurvey = (object?)null
                })
            };

            _context.ConversationStates.Add(state);
            await _context.SaveChangesAsync();
        }

        // Cache for 15 minutes
        await _cache.SetStringAsync(cacheKey, JsonSerializer.Serialize(state), 
            new DistributedCacheEntryOptions
            {
                SlidingExpiration = TimeSpan.FromMinutes(15)
            });

        return state;
    }

    public async Task<ConversationState> UpdateStateAsync(ConversationState state)
    {
        // Increment version for optimistic concurrency
        var newState = new ConversationState
        {
            ConversationId = state.ConversationId,
            Version = state.Version + 1,
            StateJson = state.StateJson,
            LastActivityUtc = DateTime.UtcNow
        };

        try
        {
            _context.ConversationStates.Add(newState);
            await _context.SaveChangesAsync();

            // Update cache
            var cacheKey = $"conversation_state:{state.ConversationId}";
            await _cache.SetStringAsync(cacheKey, JsonSerializer.Serialize(newState),
                new DistributedCacheEntryOptions
                {
                    SlidingExpiration = TimeSpan.FromMinutes(15)
                });

            return newState;
        }
        catch (DbUpdateException)
        {
            // Handle version conflict by returning the latest state
            return await GetStateAsync(state.ConversationId);
        }
    }

    public async Task<bool> TryNavigateBackAsync(Guid conversationId)
    {
        var state = await GetStateAsync(conversationId);
        
        // Parse the state JSON to check navigation history
        var stateData = JsonSerializer.Deserialize<dynamic>(state.StateJson);
        if (stateData == null) return false;

        // For now, just return false as navigation depends on the specific state structure
        // This would need to be implemented based on the actual conversation flow logic
        return false;
    }
}