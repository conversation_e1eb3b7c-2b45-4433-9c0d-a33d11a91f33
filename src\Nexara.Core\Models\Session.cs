using System.ComponentModel.DataAnnotations;

namespace Nexara.Core.Models;

public enum SessionStatus
{
    Active,
    Completed,
    Abandoned,
    Expired
}

public class Session
{
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public Guid SurveyId { get; set; }
    
    public Guid TenantId { get; set; }
    
    [MaxLength(255)]
    public string? UserExternalId { get; set; }
    
    public SessionStatus Status { get; set; } = SessionStatus.Active;
    
    public DateTime StartedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? CompletedAt { get; set; }
    
    public DateTime LastActivityAt { get; set; } = DateTime.UtcNow;
    
    public string? MetaJson { get; set; }
    
    public virtual Tenant Tenant { get; set; } = null!;
    public virtual Survey Survey { get; set; } = null!;
    public virtual ICollection<SessionState> SessionStates { get; set; } = new List<SessionState>();
    public virtual ICollection<Message> Messages { get; set; } = new List<Message>();
    public virtual ICollection<Answer> Answers { get; set; } = new List<Answer>();
    public virtual ICollection<Event> Events { get; set; } = new List<Event>();
}