using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using System.Security.Cryptography;
using System.Text;

namespace Nexara.Core.Security;

public class EmbedTokenRateLimiter : IRateLimiter
{
    private readonly IDistributedCache _cache;
    private readonly ILogger<EmbedTokenRateLimiter> _logger;

    public EmbedTokenRateLimiter(IDistributedCache cache, ILogger<EmbedTokenRateLimiter> logger)
    {
        _cache = cache;
        _logger = logger;
    }

    public async Task<bool> AllowRequestAsync(string token, int? limitPerMinute = null)
    {
        try
        {
            var limit = limitPerMinute ?? 100;
            var tokenHash = ComputeHash(token);
            var key = $"rate_limit:embed:{tokenHash}";

            var countStr = await _cache.GetStringAsync(key);
            
            if (countStr == null)
            {
                // First request within this minute
                await _cache.SetStringAsync(key, "1", new DistributedCacheEntryOptions
                {
                    SlidingExpiration = TimeSpan.FromMinutes(1)
                });
                return true;
            }

            if (!int.TryParse(countStr, out var currentCount))
            {
                _logger.LogWarning("Invalid count value in cache for rate limiting: {CountStr}", countStr);
                return false;
            }

            if (currentCount >= limit)
            {
                _logger.LogWarning("Rate limit exceeded for embed token. Current: {Current}, Limit: {Limit}", currentCount, limit);
                return false;
            }

            // Increment counter
            var newCount = currentCount + 1;
            await _cache.SetStringAsync(key, newCount.ToString(), new DistributedCacheEntryOptions
            {
                SlidingExpiration = TimeSpan.FromMinutes(1)
            });

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in rate limiting check");
            // Fail open - allow request if rate limiting check fails
            return true;
        }
    }

    private static string ComputeHash(string input)
    {
        using var sha256 = SHA256.Create();
        var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(input));
        return Convert.ToBase64String(hashBytes);
    }
}