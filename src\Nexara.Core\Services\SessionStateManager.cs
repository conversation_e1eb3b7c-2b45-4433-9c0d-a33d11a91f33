using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Nexara.Core.Data;
using Nexara.Core.Models;
using System.Text.Json;

namespace Nexara.Core.Services;

public class SessionStateManager : ISessionStateManager
{
    private readonly NexaraDbContext _dbContext;
    private readonly ISessionCache _sessionCache;
    private readonly ILogger<SessionStateManager> _logger;

    public SessionStateManager(
        NexaraDbContext dbContext,
        ISessionCache sessionCache,
        ILogger<SessionStateManager> logger)
    {
        _dbContext = dbContext;
        _sessionCache = sessionCache;
        _logger = logger;
    }

    public async Task<SessionState> GetStateAsync(Guid sessionId)
    {
        try
        {
            // Try cache first
            var cachedState = await _sessionCache.GetSessionStateAsync(sessionId);
            if (cachedState != null)
            {
                _logger.LogDebug("Retrieved session state from cache for session {SessionId}", sessionId);
                return cachedState;
            }

            // Fallback to database
            var latestState = await _dbContext.SessionStates
                .Where(s => s.SessionId == sessionId)
                .OrderByDescending(s => s.Version)
                .FirstOrDefaultAsync();

            if (latestState == null)
            {
                // Create initial state
                latestState = new SessionState
                {
                    SessionId = sessionId,
                    Version = 1,
                    AnswersJson = "{}",
                    NavigationHistoryJson = "[]",
                    ScoresJson = "{}",
                    TagsJson = "[]",
                    LastActivityUtc = DateTime.UtcNow
                };

                _dbContext.SessionStates.Add(latestState);
                await _dbContext.SaveChangesAsync();
                
                _logger.LogDebug("Created initial session state for session {SessionId}", sessionId);
            }

            // Cache the state
            await _sessionCache.SetSessionStateAsync(sessionId, latestState, TimeSpan.FromMinutes(30));

            return latestState;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving session state for session {SessionId}", sessionId);
            throw;
        }
    }

    public async Task<SessionState> UpdateStateAsync(SessionState state)
    {
        try
        {
            // Optimistic concurrency control
            var existingState = await _dbContext.SessionStates
                .Where(s => s.SessionId == state.SessionId && s.Version == state.Version)
                .FirstOrDefaultAsync();

            if (existingState == null)
            {
                // Version conflict - get latest version and throw exception
                var latestState = await _dbContext.SessionStates
                    .Where(s => s.SessionId == state.SessionId)
                    .OrderByDescending(s => s.Version)
                    .FirstOrDefaultAsync();

                var latestVersion = latestState?.Version ?? 0;
                throw new InvalidOperationException(
                    $"Version conflict. Expected version {state.Version}, but latest version is {latestVersion}");
            }

            // Create new version
            var newState = new SessionState
            {
                SessionId = state.SessionId,
                Version = state.Version + 1,
                AnswersJson = state.AnswersJson,
                NavigationHistoryJson = state.NavigationHistoryJson,
                ScoresJson = state.ScoresJson,
                TagsJson = state.TagsJson,
                LastActivityUtc = DateTime.UtcNow
            };

            _dbContext.SessionStates.Add(newState);
            await _dbContext.SaveChangesAsync();

            // Update cache
            await _sessionCache.SetSessionStateAsync(state.SessionId, newState, TimeSpan.FromMinutes(30));

            _logger.LogDebug("Updated session state for session {SessionId}, new version {Version}", 
                state.SessionId, newState.Version);

            return newState;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating session state for session {SessionId}", state.SessionId);
            throw;
        }
    }

    public async Task<bool> TryNavigateBackAsync(Guid sessionId)
    {
        try
        {
            var currentState = await GetStateAsync(sessionId);
            
            var navigationHistory = JsonSerializer.Deserialize<Stack<string>>(currentState.NavigationHistoryJson) ?? new Stack<string>();
            
            if (navigationHistory.Count <= 1)
            {
                _logger.LogDebug("Cannot navigate back - no previous nodes in history for session {SessionId}", sessionId);
                return false;
            }

            // Remove current node and go to previous
            navigationHistory.Pop();
            
            var updatedState = new SessionState
            {
                Id = currentState.Id,
                SessionId = currentState.SessionId,
                Version = currentState.Version,
                AnswersJson = currentState.AnswersJson,
                NavigationHistoryJson = JsonSerializer.Serialize(navigationHistory),
                ScoresJson = currentState.ScoresJson,
                TagsJson = currentState.TagsJson,
                LastActivityUtc = DateTime.UtcNow
            };

            await UpdateStateAsync(updatedState);
            
            _logger.LogDebug("Successfully navigated back for session {SessionId}", sessionId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error navigating back for session {SessionId}", sessionId);
            return false;
        }
    }
}