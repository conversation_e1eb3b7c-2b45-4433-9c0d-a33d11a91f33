namespace Nexara.Rules;

public enum NextAction
{
    WaitForResponse,
    EndConversation,
    TransferToHuman,
    ScheduleFollowup
}

public class RichContent
{
    public required string Type { get; set; }
    public string[]? Options { get; set; }
    public Dictionary<string, object>? Data { get; set; }
}

public class WebhookTrigger
{
    public required string Url { get; set; }
    public Dictionary<string, string>? Headers { get; set; }
    public Dictionary<string, object>? Payload { get; set; }
}

public class RuleResponse
{
    public required string Message { get; init; }
    public RichContent? RichContent { get; init; }
    public string? InitiateSurveyId { get; init; }
    public Dictionary<string, object>? UpdateState { get; init; }
    public List<string>? AddTags { get; init; }
    public WebhookTrigger? TriggerWebhook { get; init; }
    public NextAction? NextAction { get; init; }
}