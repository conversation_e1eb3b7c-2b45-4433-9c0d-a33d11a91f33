import { ReactNode } from 'react'

interface LayoutProps {
  children: ReactNode
}

export default function Layout({ children }: LayoutProps) {
  return (
    <div style={{ display: 'flex', height: '100vh' }}>
      <aside style={{ width: '250px', background: '#1e1e1e', padding: '1rem' }}>
        <h1 style={{ color: '#fff', marginBottom: '2rem' }}>Nexara</h1>
        <nav>
          <ul style={{ listStyle: 'none' }}>
            <li><a href="/" style={{ color: '#ccc', textDecoration: 'none' }}>Dashboard</a></li>
            <li><a href="/bots" style={{ color: '#ccc', textDecoration: 'none' }}>Bots</a></li>
            <li><a href="/analytics" style={{ color: '#ccc', textDecoration: 'none' }}>Analytics</a></li>
            <li><a href="/chat" style={{ color: '#ccc', textDecoration: 'none' }}>Chat</a></li>
            <li><a href="/settings" style={{ color: '#ccc', textDecoration: 'none' }}>Settings</a></li>
          </ul>
        </nav>
      </aside>
      <main style={{ flex: 1, padding: '2rem' }}>
        {children}
      </main>
    </div>
  )
}