using System.Diagnostics.Metrics;

namespace Nexara.Core.Observability;

public class SessionMetrics
{
    private readonly Meter _meter;
    private readonly Counter<long> _sessionsStarted;
    private readonly Counter<long> _sessionsCompleted;
    private readonly Counter<long> _sessionsAbandoned;
    private readonly Counter<long> _messagesDelivered;
    private readonly Histogram<double> _sessionDuration;

    public SessionMetrics()
    {
        _meter = new Meter("Nexara.Sessions", "1.0.0");
        _sessionsStarted = _meter.CreateCounter<long>("sessions.started.total", description: "Total number of sessions started");
        _sessionsCompleted = _meter.CreateCounter<long>("sessions.completed.total", description: "Total number of sessions completed");
        _sessionsAbandoned = _meter.CreateCounter<long>("sessions.abandoned.total", description: "Total number of sessions abandoned");
        _messagesDelivered = _meter.CreateCounter<long>("messages.delivered.total", description: "Total number of messages delivered");
        _sessionDuration = _meter.CreateHistogram<double>("session.duration", "seconds", "Duration of completed sessions");
    }

    public void RecordSessionStarted(Guid tenantId, Guid surveyId)
    {
        var tags = new TagList
        {
            { "tenant.id", tenantId.ToString() },
            { "survey.id", surveyId.ToString() }
        };

        _sessionsStarted.Add(1, tags);
    }

    public void RecordSessionCompleted(Guid tenantId, Guid surveyId, TimeSpan duration)
    {
        var tags = new TagList
        {
            { "tenant.id", tenantId.ToString() },
            { "survey.id", surveyId.ToString() }
        };

        _sessionsCompleted.Add(1, tags);
        _sessionDuration.Record(duration.TotalSeconds, tags);
    }

    public void RecordSessionAbandoned(Guid tenantId, Guid surveyId)
    {
        var tags = new TagList
        {
            { "tenant.id", tenantId.ToString() },
            { "survey.id", surveyId.ToString() }
        };

        _sessionsAbandoned.Add(1, tags);
    }

    public void RecordMessageDelivered(Guid tenantId, string messageType)
    {
        var tags = new TagList
        {
            { "tenant.id", tenantId.ToString() },
            { "message.type", messageType }
        };

        _messagesDelivered.Add(1, tags);
    }

    public void Dispose()
    {
        _meter?.Dispose();
    }
}