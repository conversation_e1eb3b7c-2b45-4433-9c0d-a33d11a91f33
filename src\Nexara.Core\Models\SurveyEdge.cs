using System.ComponentModel.DataAnnotations;

namespace Nexara.Core.Models;

public class SurveyEdge
{
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public Guid SurveyId { get; set; }
    
    public Guid FromNodeId { get; set; }
    
    public Guid ToNodeId { get; set; }
    
    [MaxLength(100)]
    public string? RuleKey { get; set; }
    
    public int Priority { get; set; } = 0;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public virtual Survey Survey { get; set; } = null!;
    public virtual SurveyNode FromNode { get; set; } = null!;
    public virtual SurveyNode ToNode { get; set; } = null!;
}