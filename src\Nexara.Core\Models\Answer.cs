namespace Nexara.Core.Models;

public class Answer
{
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public Guid SessionId { get; set; }
    
    public Guid NodeId { get; set; }
    
    public required string ValueJson { get; set; }
    
    public double? Confidence { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public virtual Session Session { get; set; } = null!;
    public virtual SurveyNode Node { get; set; } = null!;
}