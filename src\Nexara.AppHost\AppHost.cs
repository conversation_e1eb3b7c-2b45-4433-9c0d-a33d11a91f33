var builder = DistributedApplication.CreateBuilder(args);

// Infrastructure
var postgres = builder.AddPostgres("postgres")
    .WithDataVolume()
    .WithPgAdmin();

var nexaraDb = postgres.AddDatabase("nexara-db");

var redis = builder.AddRedis("redis")
    .WithDataVolume();

// Observability
var seq = builder.AddSeq("seq");

// Services
var apiService = builder.AddProject<Projects.Nexara_Api>("nexara-api")
    .WithReference(nexaraDb)
    .WithReference(redis)
    .WithEnvironment("Seq__ServerUrl", seq.GetEndpoint("http"));

var realtimeService = builder.AddProject<Projects.Nexara_Realtime>("nexara-realtime")
    .WithReference(nexaraDb)
    .WithReference(redis)
    .WithEnvironment("Seq__ServerUrl", seq.GetEndpoint("http"));

var workerService = builder.AddProject<Projects.Nexara_Worker>("nexara-worker")
    .WithReference(nexaraDb)
    .WithReference(redis)
    .WithEnvironment("Seq__ServerUrl", seq.GetEndpoint("http"));

builder.Build().Run();
