import { useState, useEffect, useCallback } from 'react'
import '@chatscope/chat-ui-kit-styles/dist/default/styles.min.css'
import '../styles/chat.css'
import {
  MainContainer,
  ChatContainer,
  MessageList,
  Message,
  MessageInput,
  TypingIndicator,
  MessageModel
} from '@chatscope/chat-ui-kit-react'
import { useChatStore } from '../stores/chatStore'
import { useChat } from '../hooks/useChat'
import { ChatMessage } from '../types/chat'

interface ChatProps {
  conversationId?: string
  height?: string
  className?: string
}

export const Chat: React.FC<ChatProps> = ({ 
  conversationId, 
  height = '500px',
  className = ''
}) => {
  const [inputValue, setInputValue] = useState('')
  const [isTyping, setIsTyping] = useState(false)
  
  const {
    activeConversationId,
    isConnected,
    connectionStatus,
    getActiveConversation,
    getConversationMessages,
    addMessage,
    setActiveConversation
  } = useChatStore()

  const {
    sendMessage,
    joinConversation,
    sendTypingIndicator
  } = useChat()

  // Set active conversation if provided
  useEffect(() => {
    if (conversationId && conversationId !== activeConversationId) {
      setActiveConversation(conversationId)
      if (isConnected) {
        joinConversation(conversationId)
      }
    }
  }, [conversationId, activeConversationId, setActiveConversation, joinConversation, isConnected])

  const activeConversation = getActiveConversation()
  const messages = activeConversation ? getConversationMessages(activeConversation.id) : []

  const handleSendMessage = useCallback(async (_innerHtml: string, textContent: string) => {
    if (!textContent.trim() || !activeConversation) return

    const userMessage: ChatMessage = {
      id: `temp-${Date.now()}`,
      content: textContent.trim(),
      isFromUser: true,
      timestamp: new Date(),
      sender: 'User',
      messageType: 'text'
    }

    // Add user message immediately
    addMessage(activeConversation.id, userMessage)
    setInputValue('')

    try {
      // Send via SignalR if connected
      if (isConnected) {
        await sendMessage({
          conversationId: activeConversation.id,
          content: textContent.trim(),
          messageType: 'text'
        })
      } else {
        // For demo purposes, simulate a bot response after a delay
        setTimeout(() => {
          const botMessage: ChatMessage = {
            id: `bot-${Date.now()}`,
            content: `Thanks for your message: "${textContent.trim()}". This is a demo response since the SignalR connection is not established.`,
            isFromUser: false,
            timestamp: new Date(),
            sender: 'Demo Bot',
            messageType: 'text'
          }
          addMessage(activeConversation.id, botMessage)
        }, 1000)
      }
    } catch (error) {
      console.error('Failed to send message:', error)
      // You might want to show an error state or retry mechanism here
    }
  }, [activeConversation, addMessage, sendMessage])

  const handleInputChange = useCallback((_innerHtml: string, textContent: string) => {
    setInputValue(textContent)
    
    if (activeConversation) {
      const newIsTyping = textContent.length > 0
      if (newIsTyping !== isTyping) {
        setIsTyping(newIsTyping)
        if (isConnected) {
          sendTypingIndicator(activeConversation.id, newIsTyping)
        }
      }
    }
  }, [activeConversation, isConnected, isTyping, sendTypingIndicator])

  // Convert ChatMessage to MessageModel for the chat UI
  const convertToMessageModel = (chatMessage: ChatMessage): MessageModel => ({
    message: chatMessage.content,
    sentTime: chatMessage.timestamp.toLocaleTimeString(),
    sender: chatMessage.sender,
    direction: chatMessage.isFromUser ? 'outgoing' : 'incoming',
    position: 'single' // You can implement logic for grouping consecutive messages
  })

  const getConnectionStatusMessage = () => {
    switch (connectionStatus) {
      case 'connecting':
        return 'Connecting to chat...'
      case 'disconnected':
        return 'Disconnected from chat'
      case 'error':
        return 'Connection error'
      default:
        return null
    }
  }

  const statusMessage = getConnectionStatusMessage()

  return (
    <div className={`chat-container ${className}`} style={{ position: 'relative', height }}>
      <MainContainer>
        <ChatContainer>
          <MessageList
            scrollBehavior="smooth"
            typingIndicator={isTyping ? <TypingIndicator content="Bot is typing..." /> : null}
          >
            {statusMessage && (
              <Message
                model={{
                  message: statusMessage,
                  sentTime: 'now',
                  sender: 'System',
                  direction: 'incoming',
                  position: 'single',
                  type: 'custom'
                }}
              />
            )}
            
            {messages.map((msg) => (
              <Message
                key={msg.id}
                model={convertToMessageModel(msg)}
              />
            ))}
          </MessageList>
          
          <MessageInput
            placeholder="Type a message..."
            value={inputValue}
            onChange={handleInputChange}
            onSend={handleSendMessage}
            disabled={!activeConversation}
            attachButton={false}
          />
        </ChatContainer>
      </MainContainer>
      
      {/* Connection status indicator */}
      <div className={`chat-connection-status ${
        isConnected 
          ? 'chat-connection-status--connected' 
          : connectionStatus === 'connecting' 
            ? 'chat-connection-status--connecting'
            : 'chat-connection-status--disconnected'
      }`}>
        {connectionStatus === 'connecting' 
          ? 'Connecting...' 
          : isConnected 
            ? 'Connected' 
            : 'Disconnected'
        }
      </div>
    </div>
  )
}

export default Chat