using System.ComponentModel.DataAnnotations;

namespace Nexara.Core.Models;

public enum ActionType
{
    Respond,
    TriggerSurvey,
    TransferToHuman,
    EndConversation,
    ScheduleFollowup
}

public class PromptRule
{
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public Guid RuleSetId { get; set; }
    
    [Required]
    [MaxLength(255)]
    public required string Key { get; set; }
    
    public int Priority { get; set; } = 0;
    
    [Required]
    public required string MatchPattern { get; set; }
    
    public string? ResponseTemplate { get; set; }
    
    public ActionType ActionType { get; set; } = ActionType.Respond;
    
    public string? ConfigJson { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    // Navigation properties
    public virtual RuleSet RuleSet { get; set; } = null!;
}