namespace Nexara.Core.Models;

public class SessionState
{
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public Guid SessionId { get; set; }
    
    public int Version { get; set; } = 1;
    
    public required string AnswersJson { get; set; }
    
    public required string NavigationHistoryJson { get; set; }
    
    public DateTime LastActivityUtc { get; set; } = DateTime.UtcNow;
    
    public string? ScoresJson { get; set; }
    
    public string? TagsJson { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public virtual Session Session { get; set; } = null!;
}