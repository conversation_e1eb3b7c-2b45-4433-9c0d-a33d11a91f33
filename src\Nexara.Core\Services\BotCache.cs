using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using Nexara.Core.Data;
using Nexara.Core.Models;
using Nexara.Rules;
using System.Text.Json;

namespace Nexara.Core.Services;

public class BotCache : IBotCache
{
    private readonly NexaraDbContext _context;
    private readonly IDistributedCache _cache;

    public BotCache(NexaraDbContext context, IDistributedCache cache)
    {
        _context = context;
        _cache = cache;
    }

    public async Task<Bot?> GetBotAsync(Guid botId)
    {
        var cacheKey = $"bot:{botId}";
        var cachedBot = await _cache.GetStringAsync(cacheKey);

        if (!string.IsNullOrEmpty(cachedBot))
        {
            return JsonSerializer.Deserialize<Bot>(cachedBot);
        }

        var bot = await _context.Bots
            .Include(b => b.RuleSets)
                .ThenInclude(rs => rs.PromptRules)
            .Include(b => b.Surveys)
            .FirstOrDefaultAsync(b => b.Id == botId);

        if (bot != null)
        {
            // Cache for 30 minutes
            await _cache.SetStringAsync(cacheKey, JsonSerializer.Serialize(bot),
                new DistributedCacheEntryOptions
                {
                    SlidingExpiration = TimeSpan.FromMinutes(30)
                });
        }

        return bot;
    }

    public async Task<IReadOnlyList<IPromptRule>> GetRulesAsync(Guid botId)
    {
        var cacheKey = $"bot_rules:{botId}";
        var cachedRules = await _cache.GetStringAsync(cacheKey);

        if (!string.IsNullOrEmpty(cachedRules))
        {
            // For now, return empty list as we need to implement rule compilation
            // In a real implementation, this would deserialize compiled rule instances
            return Array.Empty<IPromptRule>();
        }

        // Get active rule set for the bot
        var activeRuleSet = await _context.RuleSets
            .Include(rs => rs.PromptRules)
            .FirstOrDefaultAsync(rs => rs.BotId == botId && rs.IsActive);

        if (activeRuleSet == null)
        {
            return Array.Empty<IPromptRule>();
        }

        // TODO: Compile rules from database definitions to IPromptRule instances
        // For now, return example rules
        var rules = new List<IPromptRule>();

        // Cache compiled rules for 30 minutes
        await _cache.SetStringAsync(cacheKey, JsonSerializer.Serialize("compiled_rules_placeholder"),
            new DistributedCacheEntryOptions
            {
                SlidingExpiration = TimeSpan.FromMinutes(30)
            });

        return rules.AsReadOnly();
    }

    public async Task InvalidateBotAsync(Guid botId)
    {
        var botCacheKey = $"bot:{botId}";
        var rulesCacheKey = $"bot_rules:{botId}";

        await _cache.RemoveAsync(botCacheKey);
        await _cache.RemoveAsync(rulesCacheKey);
    }
}