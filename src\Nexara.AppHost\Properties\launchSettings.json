{"$schema": "https://json.schemastore.org/launchsettings.json", "profiles": {"https": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "applicationUrl": "https://localhost:17135;http://localhost:15178", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "DOTNET_ENVIRONMENT": "Development", "ASPIRE_DASHBOARD_OTLP_ENDPOINT_URL": "https://localhost:21277", "ASPIRE_RESOURCE_SERVICE_ENDPOINT_URL": "https://localhost:22257"}}, "http": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "applicationUrl": "http://localhost:15178", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "DOTNET_ENVIRONMENT": "Development", "ASPIRE_DASHBOARD_OTLP_ENDPOINT_URL": "http://localhost:19169", "ASPIRE_RESOURCE_SERVICE_ENDPOINT_URL": "http://localhost:20079"}}}}