using System.ComponentModel.DataAnnotations;

namespace Nexara.Core.Models;

public enum SurveyNodeType
{
    Question,
    Message,
    End
}

public class SurveyNode
{
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public Guid SurveyId { get; set; }
    
    [Required]
    [MaxLength(100)]
    public required string NodeKey { get; set; }
    
    public SurveyNodeType Type { get; set; }
    
    [Required]
    public required string ContentJson { get; set; }
    
    public string? UiSchemaJson { get; set; }
    
    public int Order { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public virtual Survey Survey { get; set; } = null!;
    public virtual ICollection<SurveyEdge> OutgoingEdges { get; set; } = new List<SurveyEdge>();
    public virtual ICollection<SurveyEdge> IncomingEdges { get; set; } = new List<SurveyEdge>();
    public virtual ICollection<Answer> Answers { get; set; } = new List<Answer>();
}