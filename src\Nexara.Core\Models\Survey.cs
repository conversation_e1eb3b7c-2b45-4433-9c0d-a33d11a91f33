using System.ComponentModel.DataAnnotations;

namespace Nexara.Core.Models;

public enum SurveyStatus
{
    Draft,
    Published,
    Archived
}

public class Survey
{
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public Guid TenantId { get; set; }
    
    [Required]
    [MaxLength(255)]
    public required string Name { get; set; }
    
    public int Version { get; set; } = 1;
    
    public SurveyStatus Status { get; set; } = SurveyStatus.Draft;
    
    public string? MetaJson { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? PublishedAt { get; set; }
    
    public virtual Tenant Tenant { get; set; } = null!;
    public virtual ICollection<SurveyNode> Nodes { get; set; } = new List<SurveyNode>();
    public virtual ICollection<SurveyEdge> Edges { get; set; } = new List<SurveyEdge>();
    public virtual ICollection<RuleDefinition> RuleDefinitions { get; set; } = new List<RuleDefinition>();
    public virtual ICollection<Session> Sessions { get; set; } = new List<Session>();
}