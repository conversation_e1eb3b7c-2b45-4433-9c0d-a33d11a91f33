using System.ComponentModel.DataAnnotations;

namespace Nexara.Core.Models;

public enum SurveyStatus
{
    Draft,
    Published,
    Archived
}

public enum TriggerType
{
    RuleTriggered,
    Scheduled,
    Proactive,
    UserRequested
}

public class Survey
{
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public Guid BotId { get; set; }
    
    [Required]
    [MaxLength(255)]
    public required string Name { get; set; }
    
    public TriggerType TriggerType { get; set; } = TriggerType.RuleTriggered;
    
    public string? TriggerConditionJson { get; set; }
    
    public string QuestionsJson { get; set; } = "[]";
    
    public SurveyStatus Status { get; set; } = SurveyStatus.Draft;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? PublishedAt { get; set; }
    
    // Navigation properties
    public virtual Bot Bot { get; set; } = null!;
    public virtual ICollection<SurveySession> SurveySessions { get; set; } = new List<SurveySession>();
}