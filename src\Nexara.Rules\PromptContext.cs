namespace Nexara.Rules;

public class PromptContext
{
    public required string UserPrompt { get; init; }
    public required Guid ConversationId { get; init; }
    public required ConversationStateData State { get; init; }
    public required Dictionary<string, object> UserAttributes { get; init; }
    public required List<MessageData> RecentHistory { get; init; }
    public required BotConfigData BotConfig { get; init; }
}

public class ConversationStateData
{
    public int Version { get; set; }
    public Guid ConversationId { get; set; }
    public Dictionary<string, object> Data { get; set; } = new();
    public Stack<string> NavigationHistory { get; set; } = new();
    public Dictionary<string, double> Scores { get; set; } = new();
    public HashSet<string> Tags { get; set; } = new();
    public DateTime LastActivityUtc { get; set; }
    public SurveyContextData? ActiveSurvey { get; set; }
}

public class SurveyContextData
{
    public Guid SurveyId { get; set; }
    public int CurrentQuestionIndex { get; set; }
    public Dictionary<string, object> Answers { get; set; } = new();
    public DateTime StartedAt { get; set; }
}

public class MessageData
{
    public Guid Id { get; set; }
    public Guid ConversationId { get; set; }
    public string Role { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public string? Intent { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class BotConfigData
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Personality { get; set; }
    public string Status { get; set; } = string.Empty;
}