{"name": "nexara-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@chatscope/chat-ui-kit-react": "^2.1.1", "@chatscope/chat-ui-kit-styles": "^1.4.0", "@microsoft/signalr": "^8.0.7", "axios": "^1.7.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.26.1", "zustand": "^4.5.5"}, "devDependencies": {"@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.15.0", "@typescript-eslint/parser": "^7.15.0", "@vitejs/plugin-react": "^4.3.1", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "typescript": "^5.2.2", "vite": "^5.3.4"}}