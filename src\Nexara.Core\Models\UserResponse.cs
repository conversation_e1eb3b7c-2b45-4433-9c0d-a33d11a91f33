namespace Nexara.Core.Models;

public class UserResponse
{
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public Guid ConversationId { get; set; }
    
    public Guid MessageId { get; set; }
    
    public string ResponseJson { get; set; } = "{}";
    
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    
    // Navigation properties
    public virtual Conversation Conversation { get; set; } = null!;
    public virtual Message Message { get; set; } = null!;
}