// Mock data utilities for testing chat functionality
import { ChatMessage, ChatConversation, MockConversationScenario } from '../types/chat'

export const generateMockMessage = (
  id: string,
  content: string,
  isFromUser: boolean,
  sender: string = isFromUser ? 'User' : 'Bot',
  timestamp: Date = new Date()
): ChatMessage => ({
  id,
  content,
  isFromUser,
  timestamp,
  sender,
  messageType: 'text'
})

export const simulateBotResponse = (_userMessage: string): ChatMessage[] => {
  const responses = [
    "Thanks for your message! How can I help you further?",
    "I understand your question. Let me provide you with some information.",
    "That's interesting! Could you tell me more about that?",
    "I'm here to assist you. What would you like to know?",
    "Great question! Here's what I can tell you about that topic."
  ]
  
  const randomResponse = responses[Math.floor(Math.random() * responses.length)]
  
  return [
    generateMockMessage(
      `bot-${Date.now()}`,
      randomResponse,
      false,
      'Nexara Bot',
      new Date(Date.now() + 1000) // 1 second delay
    )
  ]
}

export const createTestConversation = (
  id: string,
  botId: string,
  initialMessages: ChatMessage[] = []
): ChatConversation => ({
  id,
  botId,
  sessionId: `session-${id}`,
  messages: initialMessages,
  isActive: true,
  startedAt: new Date(),
  lastActivity: new Date()
})

// Export common test scenarios
export const testScenarios: MockConversationScenario[] = [
  {
    id: 'support-demo',
    name: 'Customer Support Demo',
    description: 'Demo conversation with customer support bot',
    bot: {
      id: 'support-bot',
      name: 'Support Assistant',
      description: 'Customer support chatbot'
    },
    messages: [
      generateMockMessage('1', 'Hello! How can I assist you today?', false, 'Support Assistant'),
      generateMockMessage('2', 'I need help with my account', true, 'Customer'),
      generateMockMessage('3', 'I\'d be happy to help with your account. What specific issue are you experiencing?', false, 'Support Assistant')
    ]
  },
  {
    id: 'sales-demo',
    name: 'Sales Inquiry',
    description: 'Demo conversation with sales bot',
    bot: {
      id: 'sales-bot',
      name: 'Sales Assistant',
      description: 'Sales support chatbot'
    },
    messages: [
      generateMockMessage('4', 'Welcome! I\'m here to help with your product inquiries.', false, 'Sales Assistant'),
      generateMockMessage('5', 'What are your pricing plans?', true, 'Prospect')
    ]
  }
]